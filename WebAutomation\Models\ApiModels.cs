using System.ComponentModel.DataAnnotations;

namespace WebAutomation.Models
{
    /// <summary>
    /// API响应基类
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
    }

    /// <summary>
    /// Chrome选项
    /// </summary>
    public class ChromeOptions
    {
        public bool Headless { get; set; } = false;
        public bool DisableImages { get; set; } = false;
        public bool DisableJavaScript { get; set; } = false;
        public string? UserAgent { get; set; }
        public string? ProxyServer { get; set; }
        public int WindowWidth { get; set; } = 1920;
        public int WindowHeight { get; set; } = 1080;
        public int PageLoadTimeout { get; set; } = 30;
        public List<string> Arguments { get; set; } = new List<string>();
    }

    /// <summary>
    /// 导航请求
    /// </summary>
    public class NavigateRequest
    {
        [Required]
        public string Url { get; set; } = string.Empty;
    }

    /// <summary>
    /// 元素操作请求
    /// </summary>
    public class ElementActionRequest
    {
        [Required]
        public string Selector { get; set; } = string.Empty;
        public int TimeoutSeconds { get; set; } = 10;
    }

    /// <summary>
    /// 输入文本请求
    /// </summary>
    public class InputTextRequest
    {
        [Required]
        public string Selector { get; set; } = string.Empty;
        [Required]
        public string Text { get; set; } = string.Empty;
        public int TimeoutSeconds { get; set; } = 10;
    }

    /// <summary>
    /// 爬虫请求
    /// </summary>
    public class ScrapingRequest
    {
        [Required]
        public string Url { get; set; } = string.Empty;
        public bool IncludeImages { get; set; } = true;
        public bool IncludeLinks { get; set; } = true;
        public bool ExtractImages { get; set; } = true;
        public bool ExtractLinks { get; set; } = true;
        public bool GenerateSummary { get; set; } = true;
        public int WaitTimeSeconds { get; set; } = 3;
        public string? CustomSelector { get; set; }
        public string? ContentSelector { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// 爬虫结果
    /// </summary>
    public class ScrapingResult
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Url { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string Source { get; set; } = string.Empty;
        public List<string> Images { get; set; } = new List<string>();
        public List<string> Links { get; set; } = new List<string>();
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
        public int WordCount { get; set; } = 0;
        public bool IsSuccess { get; set; } = true;
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 新闻文章
    /// </summary>
    public class NewsArticle
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public DateTime PublishedAt { get; set; }
        public string Source { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new List<string>();
        public string? ImageUrl { get; set; }
        public int ReadTime { get; set; }
    }

    /// <summary>
    /// 微信消息
    /// </summary>
    public class WeChatMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Sender { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string MessageType { get; set; } = "text";
        public string? ImageUrl { get; set; }
        public string? FileUrl { get; set; }
        public bool IsRead { get; set; } = false;
    }

    /// <summary>
    /// AI状态响应
    /// </summary>
    public class AIStatusResponse
    {
        public bool IsOnline { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool Success { get; set; }
    }

    /// <summary>
    /// 新闻源响应
    /// </summary>
    public class NewsSourcesResponse
    {
        public List<string> Sources { get; set; } = new List<string>();
        public int Count { get; set; }
        public bool Success { get; set; }
    }

    /// <summary>
    /// 摘要请求
    /// </summary>
    public class SummaryRequest
    {
        [Required]
        public string Content { get; set; } = string.Empty;
        public string? CustomPrompt { get; set; }
    }

    /// <summary>
    /// 摘要响应
    /// </summary>
    public class SummaryResponse
    {
        public string Summary { get; set; } = string.Empty;
        public bool Success { get; set; }
    }

    /// <summary>
    /// 批量新闻请求
    /// </summary>
    public class BatchNewsRequest
    {
        public List<string> Sources { get; set; } = new List<string>();
        public int ArticlesPerSource { get; set; } = 5;
    }

    /// <summary>
    /// 新闻文章请求
    /// </summary>
    public class NewsArticleRequest
    {
        [Required]
        public string Url { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
    }

    /// <summary>
    /// 系统状态
    /// </summary>
    public class SystemStatus
    {
        public bool ChromeConnected { get; set; }
        public bool AIOnline { get; set; }
        public bool WeChatLoggedIn { get; set; }
        public int NewsSourceCount { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 操作日志
    /// </summary>
    public class OperationLog
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string Operation { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string UserId { get; set; } = "System";
    }

    /// <summary>
    /// 自动化脚本
    /// </summary>
    public class AutomationScript
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<AutomationStep> Steps { get; set; } = new List<AutomationStep>();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastExecuted { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 自动化步骤
    /// </summary>
    public class AutomationStep
    {
        public int Order { get; set; }
        public string Action { get; set; } = string.Empty; // navigate, click, input, wait, screenshot
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        public string? Description { get; set; }
    }

    /// <summary>
    /// 任务调度
    /// </summary>
    public class ScheduledTask
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // scraping, news, wechat
        public string CronExpression { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        public bool IsEnabled { get; set; } = true;
        public DateTime? NextRun { get; set; }
        public DateTime? LastRun { get; set; }
    }

    /// <summary>
    /// 微信聊天
    /// </summary>
    public class WeChatChat
    {
        public string Name { get; set; } = "";
        public string LastMessage { get; set; } = "";
        public string LastTime { get; set; } = "";
        public int UnreadCount { get; set; } = 0;
        public string Avatar { get; set; } = "";
    }

}
