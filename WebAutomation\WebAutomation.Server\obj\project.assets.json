{"version": 3, "targets": {"net10.0": {"Microsoft.AspNetCore.OpenApi/10.0.0-preview.3.25172.1": {"type": "package", "dependencies": {"Microsoft.OpenApi": "2.0.0-preview.11"}, "compile": {"lib/net10.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net10.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"build/Microsoft.AspNetCore.OpenApi.targets": {}}}, "Microsoft.OpenApi/2.0.0-preview.11": {"type": "package", "compile": {"lib/net8.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}}}, "libraries": {"Microsoft.AspNetCore.OpenApi/10.0.0-preview.3.25172.1": {"sha512": "o2A0jvacBI5pyXkGwe5wO09JkBvvUAAbjF/UeAzSvw7uZ1vDBo3QD++vK8BOewf3s8/YNSwsgVerWmSAG2XPeg==", "type": "package", "path": "microsoft.aspnetcore.openapi/10.0.0-preview.3.25172.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.AspNetCore.OpenApi.SourceGenerators.dll", "build/Microsoft.AspNetCore.OpenApi.targets", "lib/net10.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net10.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.10.0.0-preview.3.25172.1.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.OpenApi/2.0.0-preview.11": {"sha512": "9Iw/y2GEKGeCCMovn2LMdnXW1G0/x7LTeO4lDYFgscw+ytEV+8Xj1Qqj1KH6TUNBqp+lc6oTfq8k7lVPn1MasA==", "type": "package", "path": "microsoft.openapi/2.0.0-preview.11", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Microsoft.OpenApi.dll", "lib/net8.0/Microsoft.OpenApi.pdb", "lib/net8.0/Microsoft.OpenApi.xml", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.2.0.0-preview.11.nupkg.sha512", "microsoft.openapi.nuspec"]}}, "projectFileDependencyGroups": {"net10.0": ["Microsoft.AspNetCore.OpenApi >= 10.0.0-preview.3.25172.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\WebAutomation.Server\\WebAutomation.Server.csproj", "projectName": "WebAutomation.Server", "projectPath": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\WebAutomation.Server\\WebAutomation.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\WebAutomation.Server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net10.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[10.0.0-preview.3.25172.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.3.25201.16/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(, 10.0.0]", "Microsoft.AspNetCore.Antiforgery": "(, 10.0.0]", "Microsoft.AspNetCore.App": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication.BearerToken": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication.Cookies": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication.Core": "(, 10.0.0]", "Microsoft.AspNetCore.Authentication.OAuth": "(, 10.0.0]", "Microsoft.AspNetCore.Authorization": "(, 10.0.0]", "Microsoft.AspNetCore.Authorization.Policy": "(, 10.0.0]", "Microsoft.AspNetCore.Components": "(, 10.0.0]", "Microsoft.AspNetCore.Components.Authorization": "(, 10.0.0]", "Microsoft.AspNetCore.Components.Endpoints": "(, 10.0.0]", "Microsoft.AspNetCore.Components.Forms": "(, 10.0.0]", "Microsoft.AspNetCore.Components.Server": "(, 10.0.0]", "Microsoft.AspNetCore.Components.Web": "(, 10.0.0]", "Microsoft.AspNetCore.Connections.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.CookiePolicy": "(, 10.0.0]", "Microsoft.AspNetCore.Cors": "(, 10.0.0]", "Microsoft.AspNetCore.Cryptography.Internal": "(, 10.0.0]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(, 10.0.0]", "Microsoft.AspNetCore.DataProtection": "(, 10.0.0]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.DataProtection.Extensions": "(, 10.0.0]", "Microsoft.AspNetCore.Diagnostics": "(, 10.0.0]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(, 10.0.0]", "Microsoft.AspNetCore.HostFiltering": "(, 10.0.0]", "Microsoft.AspNetCore.Hosting": "(, 10.0.0]", "Microsoft.AspNetCore.Hosting.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Html.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Http": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Connections": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Connections.Common": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Extensions": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Features": "(, 10.0.0]", "Microsoft.AspNetCore.Http.Results": "(, 10.0.0]", "Microsoft.AspNetCore.HttpLogging": "(, 10.0.0]", "Microsoft.AspNetCore.HttpOverrides": "(, 10.0.0]", "Microsoft.AspNetCore.HttpsPolicy": "(, 10.0.0]", "Microsoft.AspNetCore.Identity": "(, 10.0.0]", "Microsoft.AspNetCore.Localization": "(, 10.0.0]", "Microsoft.AspNetCore.Localization.Routing": "(, 10.0.0]", "Microsoft.AspNetCore.Metadata": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Core": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Cors": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Localization": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.Razor": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.RazorPages": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(, 10.0.0]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(, 10.0.0]", "Microsoft.AspNetCore.OutputCaching": "(, 10.0.0]", "Microsoft.AspNetCore.RateLimiting": "(, 10.0.0]", "Microsoft.AspNetCore.Razor": "(, 10.0.0]", "Microsoft.AspNetCore.Razor.Runtime": "(, 10.0.0]", "Microsoft.AspNetCore.RequestDecompression": "(, 10.0.0]", "Microsoft.AspNetCore.ResponseCaching": "(, 10.0.0]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.ResponseCompression": "(, 10.0.0]", "Microsoft.AspNetCore.Rewrite": "(, 10.0.0]", "Microsoft.AspNetCore.Routing": "(, 10.0.0]", "Microsoft.AspNetCore.Routing.Abstractions": "(, 10.0.0]", "Microsoft.AspNetCore.Server.HttpSys": "(, 10.0.0]", "Microsoft.AspNetCore.Server.IIS": "(, 10.0.0]", "Microsoft.AspNetCore.Server.IISIntegration": "(, 10.0.0]", "Microsoft.AspNetCore.Server.Kestrel": "(, 10.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(, 10.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(, 10.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(, 10.0.0]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(, 10.0.0]", "Microsoft.AspNetCore.Session": "(, 10.0.0]", "Microsoft.AspNetCore.SignalR": "(, 10.0.0]", "Microsoft.AspNetCore.SignalR.Common": "(, 10.0.0]", "Microsoft.AspNetCore.SignalR.Core": "(, 10.0.0]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(, 10.0.0]", "Microsoft.AspNetCore.StaticAssets": "(, 10.0.0]", "Microsoft.AspNetCore.StaticFiles": "(, 10.0.0]", "Microsoft.AspNetCore.WebSockets": "(, 10.0.0]", "Microsoft.AspNetCore.WebUtilities": "(, 10.0.0]", "Microsoft.CSharp": "(, 4.7.0]", "Microsoft.Extensions.Caching.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Caching.Memory": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.Binder": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.CommandLine": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.FileExtensions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.Ini": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.Json": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.KeyPerFile": "(, 10.0.0]", "Microsoft.Extensions.Configuration.UserSecrets": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Configuration.Xml": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.DependencyInjection": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Diagnostics": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Diagnostics.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(, 10.0.0]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(, 10.0.0]", "Microsoft.Extensions.Features": "(, 10.0.0]", "Microsoft.Extensions.FileProviders.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.FileProviders.Composite": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.FileProviders.Embedded": "(, 10.0.0]", "Microsoft.Extensions.FileProviders.Physical": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.FileSystemGlobbing": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Hosting": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Hosting.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Http": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Identity.Core": "(, 10.0.0]", "Microsoft.Extensions.Identity.Stores": "(, 10.0.0]", "Microsoft.Extensions.Localization": "(, 10.0.0]", "Microsoft.Extensions.Localization.Abstractions": "(, 10.0.0]", "Microsoft.Extensions.Logging": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.Abstractions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.Configuration": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.Console": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.Debug": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.EventLog": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.EventSource": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Logging.TraceSource": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.ObjectPool": "(, 10.0.0]", "Microsoft.Extensions.Options": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Options.DataAnnotations": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.Primitives": "(, 10.0.0-preview.3.25171.5]", "Microsoft.Extensions.WebEncoders": "(, 10.0.0]", "Microsoft.JSInterop": "(, 10.0.0]", "Microsoft.Net.Http.Headers": "(, 10.0.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.4.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "Microsoft.Win32.SystemEvents": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 5.0.0]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 10.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 4.3.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.4.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 10.0.0]", "System.Diagnostics.EventLog": "(, 10.0.0-preview.3.25171.5]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Common": "(, 5.0.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 10.0.0]", "System.Formats.Tar": "(, 10.0.0]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 4.4.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipelines": "(, 10.0.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 5.0.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.AsyncEnumerable": "(, 10.0.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 5.0.0]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 10.0.0]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 5.0.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.2]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 6.0.0]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 10.0.0]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.3.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.1]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 5.0.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Pkcs": "(, 8.0.1]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 10.0.0-preview.3.25171.5]", "System.Security.Permissions": "(, 5.0.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 10.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 10.0.0]", "System.Text.Json": "(, 10.0.0]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 10.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.RateLimiting": "(, 10.0.0-preview.3.25171.5]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 10.0.0]", "System.Threading.Tasks.Extensions": "(, 5.0.0]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Windows.Extensions": "(, 5.0.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 5.0.0]"}}}}}