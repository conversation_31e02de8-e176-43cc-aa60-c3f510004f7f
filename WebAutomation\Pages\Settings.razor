@page "/settings"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>系统设置</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">⚙️ 系统设置</MudText>

    <MudGrid>
        <!-- 系统状态 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 系统状态</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                                 Color="Color.Secondary" 
                                 Size="Size.Small"
                                 OnClick="RefreshSystemStatus">
                            刷新
                        </MudButton>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>Chrome浏览器</MudText>
                                <MudChip Color="@(_systemStatus.ChromeConnected ? Color.Success : Color.Error)" 
                                       Size="Size.Small">
                                    @(_systemStatus.ChromeConnected ? "已连接" : "未连接")
                                </MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>AI服务</MudText>
                                <MudChip Color="@(_systemStatus.AIOnline ? Color.Success : Color.Error)" 
                                       Size="Size.Small">
                                    @(_systemStatus.AIOnline ? "在线" : "离线")
                                </MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>微信登录</MudText>
                                <MudChip Color="@(_systemStatus.WeChatLoggedIn ? Color.Success : Color.Error)" 
                                       Size="Size.Small">
                                    @(_systemStatus.WeChatLoggedIn ? "已登录" : "未登录")
                                </MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>新闻源数量</MudText>
                                <MudChip Color="Color.Info" Size="Size.Small">
                                    @_systemStatus.NewsSourceCount
                                </MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>最后更新</MudText>
                                <MudText Typo="Typo.caption">
                                    @_systemStatus.LastUpdated.ToString("yyyy-MM-dd HH:mm:ss")
                                </MudText>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 操作日志 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4" Style="height: 400px;">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📝 操作日志</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudButton StartIcon="Icons.Material.Filled.Delete" 
                                 Color="Color.Error" 
                                 Size="Size.Small"
                                 OnClick="ClearLogs">
                            清空日志
                        </MudButton>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent Style="height: 300px; overflow-y: auto;">
                    @if (_operationLogs?.Count > 0)
                    {
                        <MudList>
                            @foreach (var log in _operationLogs.Take(20))
                            {
                                <MudListItem>
                                    <div>
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@(log.Success ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" 
                                                   Color="@(log.Success ? Color.Success : Color.Error)" 
                                                   Size="Size.Small" Class="mr-2" />
                                            <MudText Typo="Typo.body2">@log.Operation</MudText>
                                            <MudSpacer />
                                            <MudText Typo="Typo.caption">
                                                @log.Timestamp.ToString("HH:mm:ss")
                                            </MudText>
                                        </div>
                                        <MudText Typo="Typo.caption" Class="ml-6">
                                            @log.Details
                                        </MudText>
                                        @if (!string.IsNullOrEmpty(log.ErrorMessage))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Error" Class="ml-6">
                                                错误: @log.ErrorMessage
                                            </MudText>
                                        }
                                    </div>
                                </MudListItem>
                            }
                        </MudList>
                    }
                    else
                    {
                        <MudText Typo="Typo.body2" Class="text-center">暂无操作日志</MudText>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 数据管理 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💾 数据管理</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Warning" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.CleaningServices"
                                     OnClick="CleanupOldData"
                                     Disabled="_isLoading">
                                清理旧数据
                            </MudButton>
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Info" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Download"
                                     OnClick="ExportData"
                                     Disabled="_isLoading">
                                导出数据
                            </MudButton>
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Error" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.DeleteForever"
                                     OnClick="ClearAllData"
                                     Disabled="_isLoading">
                                清空所有数据
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 系统信息 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">ℹ️ 系统信息</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>应用版本</MudText>
                                <MudText Typo="Typo.caption">v1.0.0</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>.NET版本</MudText>
                                <MudText Typo="Typo.caption">8.0</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>Blazor模式</MudText>
                                <MudText Typo="Typo.caption">Server</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>启动时间</MudText>
                                <MudText Typo="Typo.caption">@_startupTime</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center justify-space-between">
                                <MudText>运行时间</MudText>
                                <MudText Typo="Typo.caption">@_uptime</MudText>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 配置设置 -->
        <MudItem xs="12">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🔧 配置设置</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_deepSeekApiKey" 
                                        Label="DeepSeek API密钥" 
                                        InputType="InputType.Password"
                                        Placeholder="输入您的DeepSeek API密钥" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_deepSeekBaseUrl" 
                                        Label="DeepSeek API地址" 
                                        Placeholder="https://api.deepseek.com/v1" />
                        </MudItem>
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="_maxConcurrentTasks" 
                                           Label="最大并发任务数" 
                                           Min="1" Max="10" />
                        </MudItem>
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="_requestTimeout" 
                                           Label="请求超时时间(秒)" 
                                           Min="5" Max="300" />
                        </MudItem>
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="_dataRetentionDays" 
                                           Label="数据保留天数" 
                                           Min="1" Max="365" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Primary" 
                                     StartIcon="Icons.Material.Filled.Save"
                                     OnClick="SaveSettings"
                                     Disabled="_isLoading">
                                保存设置
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

@code {
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private SystemStatus _systemStatus = new();
    private List<OperationLog> _operationLogs = new();
    private string _startupTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string _uptime = "00:00:00";
    
    // 配置设置
    private string _deepSeekApiKey = "";
    private string _deepSeekBaseUrl = "https://api.deepseek.com/v1";
    private int _maxConcurrentTasks = 3;
    private int _requestTimeout = 30;
    private int _dataRetentionDays = 30;

    private Timer? _uptimeTimer;

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        _systemStatus = StateService.SystemStatus;
        _operationLogs = StateService.OperationLogs;
        
        await RefreshSystemStatus();
        
        // 启动运行时间计时器
        _uptimeTimer = new Timer(UpdateUptime, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    private void UpdateUptime(object? state)
    {
        var uptime = DateTime.Now - DateTime.Today;
        _uptime = $"{uptime.Hours:D2}:{uptime.Minutes:D2}:{uptime.Seconds:D2}";
        InvokeAsync(StateHasChanged);
    }

    private async Task RefreshSystemStatus()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/automation/system/status");
            if (response.IsSuccessStatusCode)
            {
                _systemStatus = await response.Content.ReadFromJsonAsync<SystemStatus>() ?? new SystemStatus();
                await StateService.UpdateSystemStatusAsync(_systemStatus);
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"刷新系统状态失败: {ex.Message}");
        }
    }

    private async Task ClearLogs()
    {
        _isLoading = true;
        _loadingMessage = "正在清空日志...";
        
        try
        {
            await StateService.ClearOperationLogsAsync();
            _operationLogs.Clear();
            await NotificationService.ShowSuccessAsync("操作日志已清空");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"清空日志失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task CleanupOldData()
    {
        _isLoading = true;
        _loadingMessage = "正在清理旧数据...";
        
        try
        {
            var response = await HttpClient.PostAsync("/api/automation/system/cleanup", null);
            if (response.IsSuccessStatusCode)
            {
                await NotificationService.ShowSuccessAsync("旧数据清理完成");
            }
            else
            {
                await NotificationService.ShowErrorAsync("清理旧数据失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"清理旧数据时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ExportData()
    {
        _isLoading = true;
        _loadingMessage = "正在导出数据...";
        
        try
        {
            // 这里可以实现数据导出功能
            await Task.Delay(2000); // 模拟导出过程
            await NotificationService.ShowSuccessAsync("数据导出完成");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"导出数据失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ClearAllData()
    {
        _isLoading = true;
        _loadingMessage = "正在清空所有数据...";
        
        try
        {
            await StateService.ClearAllDataAsync();
            _operationLogs.Clear();
            _systemStatus = new SystemStatus();
            await NotificationService.ShowWarningAsync("所有数据已清空");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"清空数据失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task SaveSettings()
    {
        _isLoading = true;
        _loadingMessage = "正在保存设置...";
        
        try
        {
            // 这里可以实现保存配置的功能
            await Task.Delay(1000); // 模拟保存过程
            await NotificationService.ShowSuccessAsync("设置保存成功");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"保存设置失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
        _uptimeTimer?.Dispose();
    }
}
