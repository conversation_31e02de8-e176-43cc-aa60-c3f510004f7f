using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using WebAutomation.Server.Models;
using System.Collections.ObjectModel;

namespace WebAutomation.Server.Services
{
    /// <summary>
    /// Chrome自动化服务
    /// </summary>
    public class ChromeService : IDisposable
    {
        private ChromeDriver? _driver;
        private readonly ILogger<ChromeService> _logger;
        private bool _disposed = false;

        public ChromeService(ILogger<ChromeService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 初始化Chrome浏览器
        /// </summary>
        public async Task<bool> InitializeBrowserAsync(Models.ChromeOptions? options = null)
        {
            try
            {
                var chromeOptions = CreateChromeOptions(options);
                _driver = new ChromeDriver(chromeOptions);
                
                if (options != null)
                {
                    _driver.Manage().Window.Size = new System.Drawing.Size(options.WindowWidth, options.WindowHeight);
                    _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(options.PageLoadTimeout);
                }
                
                _logger.LogInformation("Chrome浏览器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Chrome浏览器初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        public async Task<bool> NavigateToAsync(string url)
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                _driver.Navigate().GoToUrl(url);
                await Task.Delay(2000); // 等待页面加载
                
                _logger.LogInformation($"成功导航到: {url}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"导航到 {url} 失败");
                return false;
            }
        }

        /// <summary>
        /// 点击元素
        /// </summary>
        public async Task<bool> ClickElementAsync(string selector, int timeoutSeconds = 10)
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                var element = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                
                element.Click();
                await Task.Delay(1000);
                
                _logger.LogInformation($"成功点击元素: {selector}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"点击元素 {selector} 失败");
                return false;
            }
        }

        /// <summary>
        /// 输入文本
        /// </summary>
        public async Task<bool> InputTextAsync(string selector, string text, int timeoutSeconds = 10)
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                var element = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                
                element.Clear();
                element.SendKeys(text);
                await Task.Delay(500);
                
                _logger.LogInformation($"成功输入文本到元素: {selector}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"输入文本到元素 {selector} 失败");
                return false;
            }
        }

        /// <summary>
        /// 获取页面源码
        /// </summary>
        public string GetPageSource()
        {
            if (_driver == null)
            {
                throw new InvalidOperationException("浏览器未初始化");
            }

            return _driver.PageSource;
        }

        /// <summary>
        /// 获取页面标题
        /// </summary>
        public string GetPageTitle()
        {
            if (_driver == null)
            {
                throw new InvalidOperationException("浏览器未初始化");
            }

            return _driver.Title;
        }

        /// <summary>
        /// 获取当前URL
        /// </summary>
        public string GetCurrentUrl()
        {
            if (_driver == null)
            {
                throw new InvalidOperationException("浏览器未初始化");
            }

            return _driver.Url;
        }

        /// <summary>
        /// 等待元素出现
        /// </summary>
        public async Task<IWebElement?> WaitForElementAsync(string selector, int timeoutSeconds = 10)
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                return wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"等待元素 {selector} 超时");
                return null;
            }
        }

        /// <summary>
        /// 获取元素文本
        /// </summary>
        public async Task<string?> GetElementTextAsync(string selector, int timeoutSeconds = 10)
        {
            try
            {
                var element = await WaitForElementAsync(selector, timeoutSeconds);
                return element?.Text;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取元素 {selector} 文本失败");
                return null;
            }
        }

        /// <summary>
        /// 截图
        /// </summary>
        public async Task<byte[]?> TakeScreenshotAsync()
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                var screenshot = ((ITakesScreenshot)_driver).GetScreenshot();
                return screenshot.AsByteArray;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "截图失败");
                return null;
            }
        }

        /// <summary>
        /// 执行JavaScript
        /// </summary>
        public async Task<object?> ExecuteJavaScriptAsync(string script)
        {
            try
            {
                if (_driver == null)
                {
                    throw new InvalidOperationException("浏览器未初始化");
                }

                var jsExecutor = (IJavaScriptExecutor)_driver;
                return jsExecutor.ExecuteScript(script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行JavaScript失败");
                return null;
            }
        }

        /// <summary>
        /// 创建Chrome选项
        /// </summary>
        private OpenQA.Selenium.Chrome.ChromeOptions CreateChromeOptions(Models.ChromeOptions? options)
        {
            var chromeOptions = new OpenQA.Selenium.Chrome.ChromeOptions();
            
            // 基本设置
            chromeOptions.AddArgument("--no-sandbox");
            chromeOptions.AddArgument("--disable-dev-shm-usage");
            chromeOptions.AddArgument("--disable-blink-features=AutomationControlled");
            chromeOptions.AddExcludedArgument("enable-automation");
            chromeOptions.AddAdditionalOption("useAutomationExtension", false);
            
            if (options != null)
            {
                if (options.Headless)
                {
                    chromeOptions.AddArgument("--headless");
                }
                
                if (options.DisableImages)
                {
                    chromeOptions.AddArgument("--disable-images");
                }
                
                if (!string.IsNullOrEmpty(options.UserAgent))
                {
                    chromeOptions.AddArgument($"--user-agent={options.UserAgent}");
                }
                
                if (!string.IsNullOrEmpty(options.ProxyServer))
                {
                    chromeOptions.AddArgument($"--proxy-server={options.ProxyServer}");
                }
                
                foreach (var arg in options.Arguments)
                {
                    chromeOptions.AddArgument(arg);
                }
            }
            
            return chromeOptions;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _driver?.Quit();
                _driver?.Dispose();
                _disposed = true;
                _logger.LogInformation("Chrome浏览器已关闭");
            }
        }
    }
}
