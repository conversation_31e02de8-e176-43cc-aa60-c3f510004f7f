namespace WebAutomation.Services
{
    public class NotificationService
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly StateService _stateService;

        public NotificationService(ILogger<NotificationService> logger, StateService stateService)
        {
            _logger = logger;
            _stateService = stateService;
        }

        /// <summary>
        /// 显示成功消息
        /// </summary>
        public async Task ShowSuccessAsync(string message, string? operation = null, string? details = null)
        {
            _logger.LogInformation("成功: {Message}", message);

            if (!string.IsNullOrEmpty(operation))
            {
                await _stateService.AddOperationLogAsync(operation, details ?? message, true);
            }
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        public async Task ShowErrorAsync(string message, string? operation = null, string? details = null)
        {
            _logger.LogError("错误: {Message}", message);

            if (!string.IsNullOrEmpty(operation))
            {
                await _stateService.AddOperationLogAsync(operation, details ?? message, false, message);
            }
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        public async Task ShowWarningAsync(string message, string? operation = null, string? details = null)
        {
            _logger.LogWarning("警告: {Message}", message);

            if (!string.IsNullOrEmpty(operation))
            {
                await _stateService.AddOperationLogAsync(operation, details ?? message, true);
            }
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        public async Task ShowInfoAsync(string message, string? operation = null, string? details = null)
        {
            _logger.LogInformation("信息: {Message}", message);

            if (!string.IsNullOrEmpty(operation))
            {
                await _stateService.AddOperationLogAsync(operation, details ?? message, true);
            }
        }

        /// <summary>
        /// 显示Chrome操作结果
        /// </summary>
        public async Task ShowChromeResultAsync(bool success, string operation, string? details = null)
        {
            if (success)
            {
                await ShowSuccessAsync($"Chrome {operation} 成功", $"Chrome {operation}", details);
                await _stateService.UpdateChromeStatusAsync(true);
            }
            else
            {
                await ShowErrorAsync($"Chrome {operation} 失败", $"Chrome {operation}", details);
            }
        }

        /// <summary>
        /// 显示爬虫操作结果
        /// </summary>
        public async Task ShowScrapingResultAsync(bool success, string url, string? details = null)
        {
            if (success)
            {
                await ShowSuccessAsync($"网页爬取成功: {url}", "网页爬取", details);
            }
            else
            {
                await ShowErrorAsync($"网页爬取失败: {url}", "网页爬取", details);
            }
        }

        /// <summary>
        /// 显示微信操作结果
        /// </summary>
        public async Task ShowWeChatResultAsync(bool success, string operation, string? details = null)
        {
            if (success)
            {
                await ShowSuccessAsync($"微信 {operation} 成功", $"微信 {operation}", details);
                if (operation == "登录")
                {
                    await _stateService.UpdateWeChatStatusAsync(true);
                }
            }
            else
            {
                await ShowErrorAsync($"微信 {operation} 失败", $"微信 {operation}", details);
            }
        }

        /// <summary>
        /// 显示AI操作结果
        /// </summary>
        public async Task ShowAIResultAsync(bool success, string operation, string? details = null)
        {
            if (success)
            {
                await ShowSuccessAsync($"AI {operation} 成功", $"AI {operation}", details);
                await _stateService.UpdateAIStatusAsync(true);
            }
            else
            {
                await ShowErrorAsync($"AI {operation} 失败", $"AI {operation}", details);
            }
        }

        /// <summary>
        /// 显示新闻爬取结果
        /// </summary>
        public async Task ShowNewsResultAsync(bool success, string source, int count = 0, string? details = null)
        {
            if (success)
            {
                await ShowSuccessAsync($"新闻爬取成功: {source} ({count} 篇)", "新闻爬取", details);
            }
            else
            {
                await ShowErrorAsync($"新闻爬取失败: {source}", "新闻爬取", details);
            }
        }

        /// <summary>
        /// 显示系统状态更新
        /// </summary>
        public async Task ShowSystemStatusAsync(string component, bool online, string? details = null)
        {
            var status = online ? "在线" : "离线";
            var message = $"{component} 状态: {status}";
            
            if (online)
            {
                await ShowInfoAsync(message, "系统状态检查", details);
            }
            else
            {
                await ShowWarningAsync(message, "系统状态检查", details);
            }
        }

        /// <summary>
        /// 显示批量操作进度
        /// </summary>
        public void ShowProgress(string message)
        {
            _logger.LogInformation("进度: {Message}", message);
        }

        /// <summary>
        /// 显示操作完成
        /// </summary>
        public async Task ShowCompletionAsync(string operation, int successCount, int totalCount, string? details = null)
        {
            var message = $"{operation} 完成: {successCount}/{totalCount} 成功";
            
            if (successCount == totalCount)
            {
                await ShowSuccessAsync(message, operation, details);
            }
            else if (successCount > 0)
            {
                await ShowWarningAsync(message, operation, details);
            }
            else
            {
                await ShowErrorAsync(message, operation, details);
            }
        }
    }
}
