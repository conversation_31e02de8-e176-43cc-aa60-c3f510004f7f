# WebAutomation - Chrome自动化控制 & 智能网页爬虫平台

一个功能强大的.NET Core项目，提供Chrome浏览器自动化控制、网页爬虫、微信网页版操作和AI智能分析功能。

## 🚀 主要功能

### 1. Chrome浏览器自动化控制
- 🌐 完全控制Chrome浏览器（支持有头/无头模式）
- 🖱️ 自动化网页操作（点击、输入、导航等）
- 📸 网页截图功能
- ⚙️ 灵活的浏览器配置选项

### 2. 智能网页爬虫
- 🕷️ 高效的网页内容提取
- 🖼️ 自动提取图片和链接
- 📄 智能内容解析和清理
- 🔧 支持自定义选择器

### 3. 微信网页版支持
- 💬 微信网页版自动登录
- 📱 获取聊天列表和消息
- 💌 自动发送消息
- 📊 消息内容分析

### 4. 科技新闻爬虫
- 📰 支持多个主流科技新闻网站
  - 36氪 (36kr.com)
  - IT之家 (ithome.com)
  - cnBeta (cnbeta.com)
  - TechCrunch (techcrunch.com)
- 🔄 批量爬取多个新闻源
- 📈 自动提取文章关键信息

### 5. AI智能分析 (DeepSeek API)
- 🤖 内容智能摘要生成
- 📊 新闻文章深度分析
- 💭 微信消息情感分析
- 🔍 技术文章专业解读
- 🎯 自定义分析任务

## 🛠️ 技术栈

- **后端**: .NET Core 10.0
- **浏览器自动化**: Selenium WebDriver + ChromeDriver
- **HTML解析**: HtmlAgilityPack
- **AI服务**: DeepSeek API
- **前端**: HTML5 + CSS3 + JavaScript
- **API**: RESTful Web API

## 📦 安装和配置

### 1. 环境要求
- .NET 10.0 SDK
- Chrome浏览器
- Visual Studio 2022 或 VS Code

### 2. 克隆项目
```bash
git clone <repository-url>
cd WebAutomation
```

### 3. 安装依赖
```bash
dotnet restore
```

### 4. 配置DeepSeek API
编辑 `appsettings.json` 文件，添加您的DeepSeek API密钥：

```json
{
  "DeepSeek": {
    "ApiKey": "YOUR_DEEPSEEK_API_KEY_HERE",
    "BaseUrl": "https://api.deepseek.com/v1"
  }
}
```

### 5. 运行项目
```bash
dotnet run
```

项目将在 `https://localhost:5001` 启动。

## 🎯 使用方法

### Web界面
访问 `https://localhost:5001` 查看Web控制台界面，可以直接在浏览器中操作所有功能。

### API接口

#### Chrome控制
```bash
# 初始化Chrome浏览器
POST /api/automation/chrome/init
{
  "headless": false,
  "disableImages": false,
  "windowWidth": 1920,
  "windowHeight": 1080
}

# 导航到指定URL
POST /api/automation/chrome/navigate
{
  "url": "https://example.com"
}

# 点击元素
POST /api/automation/chrome/click
{
  "selector": ".button-class",
  "timeoutSeconds": 10
}

# 截图
GET /api/automation/chrome/screenshot
```

#### 网页爬虫
```bash
# 爬取网页内容
POST /api/automation/scraping/webpage
{
  "url": "https://example.com",
  "includeImages": true,
  "includeLinks": true,
  "generateSummary": true,
  "waitTimeSeconds": 3
}
```

#### 微信操作
```bash
# 登录微信
POST /api/automation/wechat/login

# 获取聊天列表
GET /api/automation/wechat/chats

# 获取消息
GET /api/automation/wechat/messages?maxCount=50

# 分析消息
POST /api/automation/wechat/analyze
[
  {
    "sender": "张三",
    "content": "今天天气不错",
    "timestamp": "2024-01-01T10:00:00Z"
  }
]
```

#### 新闻爬虫
```bash
# 获取支持的新闻源
GET /api/automation/news/sources

# 爬取指定新闻源
GET /api/automation/news/36kr?maxArticles=10

# 批量爬取多个新闻源
POST /api/automation/news/batch
{
  "sources": ["36kr", "ithome", "cnbeta"],
  "articlesPerSource": 5
}
```

#### AI分析
```bash
# 生成内容摘要
POST /api/automation/ai/summary
{
  "content": "要分析的文本内容...",
  "customPrompt": "自定义分析提示词（可选）"
}

# 检查AI服务状态
GET /api/automation/ai/status
```

## 🔧 配置选项

### Chrome配置
```json
{
  "Chrome": {
    "DefaultHeadless": false,
    "DefaultWindowWidth": 1920,
    "DefaultWindowHeight": 1080,
    "DefaultPageLoadTimeout": 30
  }
}
```

### 爬虫配置
```json
{
  "WebScraping": {
    "DefaultWaitTime": 3,
    "MaxRetries": 3,
    "RequestDelay": 1000
  }
}
```

## 🚨 注意事项

1. **Chrome浏览器**: 确保系统已安装Chrome浏览器
2. **DeepSeek API**: 需要有效的API密钥才能使用AI分析功能
3. **微信网页版**: 需要手动扫码登录，登录状态会保持一段时间
4. **反爬虫**: 某些网站可能有反爬虫机制，请合理使用
5. **法律合规**: 请确保爬虫行为符合目标网站的robots.txt和使用条款

## 🔒 安全建议

- 不要在公共环境中暴露API密钥
- 建议在生产环境中使用HTTPS
- 定期更新依赖包以修复安全漏洞
- 对敏感操作添加身份验证

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请创建Issue或联系开发者。

---

**享受自动化的乐趣！** 🎉
