// WebAutomation 应用JavaScript功能

window.webAutomation = {
    // 深色模式切换
    toggleDarkMode: function (isDark) {
        if (isDark) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('darkMode', 'true');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('darkMode', 'false');
        }
    },

    // 初始化深色模式
    initDarkMode: function () {
        const isDark = localStorage.getItem('darkMode') === 'true';
        if (isDark) {
            document.body.classList.add('dark-mode');
        }
        return isDark;
    },

    // 复制到剪贴板
    copyToClipboard: async function (text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('复制失败:', err);
            return false;
        }
    },

    // 下载文件
    downloadFile: function (filename, content, contentType = 'text/plain') {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    },

    // 下载JSON文件
    downloadJson: function (filename, data) {
        const json = JSON.stringify(data, null, 2);
        this.downloadFile(filename, json, 'application/json');
    },

    // 下载CSV文件
    downloadCsv: function (filename, data) {
        if (!Array.isArray(data) || data.length === 0) {
            console.error('CSV数据必须是非空数组');
            return;
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => {
                const value = row[header];
                // 处理包含逗号或引号的值
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            }).join(','))
        ].join('\n');

        this.downloadFile(filename, csvContent, 'text/csv');
    },

    // 全屏功能
    toggleFullscreen: function () {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            return true;
        } else {
            document.exitFullscreen();
            return false;
        }
    },

    // 滚动到顶部
    scrollToTop: function () {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    },

    // 滚动到元素
    scrollToElement: function (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    },

    // 获取设备信息
    getDeviceInfo: function () {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screenWidth: screen.width,
            screenHeight: screen.height,
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight
        };
    },

    // 检查网络状态
    isOnline: function () {
        return navigator.onLine;
    },

    // 监听网络状态变化
    onNetworkChange: function (callback) {
        window.addEventListener('online', () => callback(true));
        window.addEventListener('offline', () => callback(false));
    },

    // 本地存储操作
    storage: {
        set: function (key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (err) {
                console.error('存储失败:', err);
                return false;
            }
        },

        get: function (key) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : null;
            } catch (err) {
                console.error('读取失败:', err);
                return null;
            }
        },

        remove: function (key) {
            localStorage.removeItem(key);
        },

        clear: function () {
            localStorage.clear();
        }
    },

    // 通知功能
    notification: {
        // 检查通知权限
        checkPermission: function () {
            return Notification.permission;
        },

        // 请求通知权限
        requestPermission: async function () {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                return permission;
            }
            return 'denied';
        },

        // 显示通知
        show: function (title, options = {}) {
            if (Notification.permission === 'granted') {
                const notification = new Notification(title, {
                    icon: '/favicon.png',
                    badge: '/favicon.png',
                    ...options
                });

                // 自动关闭通知
                if (options.autoClose !== false) {
                    setTimeout(() => notification.close(), options.duration || 5000);
                }

                return notification;
            }
            return null;
        }
    },

    // 性能监控
    performance: {
        // 获取页面加载时间
        getLoadTime: function () {
            const navigation = performance.getEntriesByType('navigation')[0];
            return navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0;
        },

        // 获取内存使用情况
        getMemoryUsage: function () {
            if ('memory' in performance) {
                return {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                };
            }
            return null;
        },

        // 标记性能点
        mark: function (name) {
            performance.mark(name);
        },

        // 测量性能
        measure: function (name, startMark, endMark) {
            performance.measure(name, startMark, endMark);
            const measure = performance.getEntriesByName(name)[0];
            return measure ? measure.duration : 0;
        }
    },

    // 工具函数
    utils: {
        // 防抖函数
        debounce: function (func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 节流函数
        throttle: function (func, limit) {
            let inThrottle;
            return function (...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // 格式化文件大小
        formatFileSize: function (bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // 格式化时间
        formatTime: function (date) {
            return new Intl.DateTimeFormat('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).format(new Date(date));
        },

        // 生成UUID
        generateUUID: function () {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
    }
};

// 初始化应用
document.addEventListener('DOMContentLoaded', function () {
    // 初始化深色模式
    const isDark = webAutomation.initDarkMode();
    
    // 监听网络状态变化
    webAutomation.onNetworkChange(function (isOnline) {
        console.log('网络状态:', isOnline ? '在线' : '离线');
    });

    // 性能标记
    webAutomation.performance.mark('app-initialized');
});

// 全局错误处理
window.addEventListener('error', function (event) {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', function (event) {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 导出到全局
window.toggleDarkMode = webAutomation.toggleDarkMode;
