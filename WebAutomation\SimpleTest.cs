using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Linq;
using System.Collections.Generic;

namespace WebAutomation
{
    class SimpleTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== WebAutomation 爬虫功能测试 ===");
            Console.WriteLine();

            try
            {
                // 读取测试HTML文件
                string htmlContent = File.ReadAllText("test.html");
                Console.WriteLine("✅ 成功读取测试HTML文件");
                Console.WriteLine($"📄 文件大小: {htmlContent.Length} 字符");
                Console.WriteLine();

                // 提取标题
                var titleMatch = Regex.Match(htmlContent, @"<title[^>]*>([^<]+)</title>", RegexOptions.IgnoreCase);
                string title = titleMatch.Success ? titleMatch.Groups[1].Value.Trim() : "未找到标题";
                Console.WriteLine($"📰 页面标题: {title}");
                Console.WriteLine();

                // 提取新闻文章
                var articleMatches = Regex.Matches(htmlContent, @"<article[^>]*>(.*?)</article>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                Console.WriteLine($"📋 发现 {articleMatches.Count} 篇新闻文章:");
                Console.WriteLine();

                var techNews = new List<NewsItem>();

                for (int i = 0; i < articleMatches.Count; i++)
                {
                    string articleHtml = articleMatches[i].Groups[1].Value;
                    
                    // 提取文章标题
                    var h2Match = Regex.Match(articleHtml, @"<h2[^>]*>([^<]+)</h2>", RegexOptions.IgnoreCase);
                    string articleTitle = h2Match.Success ? h2Match.Groups[1].Value.Trim() : $"文章 {i + 1}";
                    
                    // 提取发布时间
                    var timeMatch = Regex.Match(articleHtml, @"发布时间：([^|]+)", RegexOptions.IgnoreCase);
                    string publishTime = timeMatch.Success ? timeMatch.Groups[1].Value.Trim() : "未知时间";
                    
                    // 提取内容
                    var contentMatch = Regex.Match(articleHtml, @"<div class=""content""[^>]*>(.*?)</div>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                    string content = "";
                    if (contentMatch.Success)
                    {
                        content = Regex.Replace(contentMatch.Groups[1].Value, @"<[^>]+>", "").Trim();
                        content = Regex.Replace(content, @"\s+", " ");
                    }
                    
                    // 提取标签
                    var tagMatches = Regex.Matches(articleHtml, @"<span class=""tag""[^>]*>([^<]+)</span>", RegexOptions.IgnoreCase);
                    var tags = tagMatches.Cast<Match>().Select(m => m.Groups[1].Value.Trim()).ToList();
                    
                    var newsItem = new NewsItem
                    {
                        Title = articleTitle,
                        PublishTime = publishTime,
                        Content = content,
                        Tags = tags
                    };
                    
                    techNews.Add(newsItem);
                    
                    Console.WriteLine($"🔸 文章 {i + 1}: {articleTitle}");
                    Console.WriteLine($"   ⏰ 时间: {publishTime}");
                    Console.WriteLine($"   🏷️ 标签: {string.Join(", ", tags)}");
                    Console.WriteLine($"   📝 内容: {(content.Length > 100 ? content.Substring(0, 100) + "..." : content)}");
                    Console.WriteLine();
                }

                // 生成科技新闻摘要
                Console.WriteLine("=== 🤖 科技新闻智能摘要 ===");
                Console.WriteLine();
                
                // 统计关键词
                var allTags = techNews.SelectMany(n => n.Tags).ToList();
                var tagCounts = allTags.GroupBy(t => t).ToDictionary(g => g.Key, g => g.Count());
                var topTags = tagCounts.OrderByDescending(kv => kv.Value).Take(5).ToList();
                
                Console.WriteLine("📊 热门科技话题:");
                foreach (var tag in topTags)
                {
                    Console.WriteLine($"   • {tag.Key} ({tag.Value} 次提及)");
                }
                Console.WriteLine();
                
                // 分类新闻
                var aiNews = techNews.Where(n => n.Tags.Any(t => t.Contains("人工智能") || t.Contains("AI") || t.Contains("ChatGPT"))).ToList();
                var mobileNews = techNews.Where(n => n.Tags.Any(t => t.Contains("手机") || t.Contains("iPhone") || t.Contains("华为"))).ToList();
                var autoNews = techNews.Where(n => n.Tags.Any(t => t.Contains("自动驾驶") || t.Contains("特斯拉"))).ToList();
                
                Console.WriteLine("📱 移动设备新闻:");
                foreach (var news in mobileNews)
                {
                    Console.WriteLine($"   • {news.Title}");
                }
                Console.WriteLine();
                
                Console.WriteLine("🤖 人工智能新闻:");
                foreach (var news in aiNews)
                {
                    Console.WriteLine($"   • {news.Title}");
                }
                Console.WriteLine();
                
                Console.WriteLine("🚗 自动驾驶新闻:");
                foreach (var news in autoNews)
                {
                    Console.WriteLine($"   • {news.Title}");
                }
                Console.WriteLine();
                
                // 生成总结
                Console.WriteLine("📋 今日科技新闻总结:");
                Console.WriteLine($"   📊 共发现 {techNews.Count} 篇科技新闻");
                Console.WriteLine($"   📱 移动设备相关: {mobileNews.Count} 篇");
                Console.WriteLine($"   🤖 人工智能相关: {aiNews.Count} 篇");
                Console.WriteLine($"   🚗 自动驾驶相关: {autoNews.Count} 篇");
                Console.WriteLine();
                
                Console.WriteLine("🔥 重点关注:");
                Console.WriteLine("   • 苹果iPhone 15系列发布，A17芯片性能提升显著");
                Console.WriteLine("   • ChatGPT-4多模态能力突破，AI技术持续进步");
                Console.WriteLine("   • 华为鸿蒙4.0生态建设加速，设备兼容性增强");
                Console.WriteLine("   • 特斯拉FSD 4.0芯片发布，自动驾驶技术再进一步");
                Console.WriteLine();
                
                Console.WriteLine("✅ 爬虫功能测试完成！");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
    
    public class NewsItem
    {
        public string Title { get; set; } = "";
        public string PublishTime { get; set; } = "";
        public string Content { get; set; } = "";
        public List<string> Tags { get; set; } = new List<string>();
    }
}
