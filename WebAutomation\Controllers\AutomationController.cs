using Microsoft.AspNetCore.Mvc;
using WebAutomation.Models;
using WebAutomation.Services;
using WebAutomation.Data;

namespace WebAutomation.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AutomationController : ControllerBase
    {
        private readonly ChromeService _chromeService;
        private readonly WebScrapingService _webScrapingService;
        private readonly WeChatService _weChatService;
        private readonly NewsScrapingService _newsScrapingService;
        private readonly DeepSeekService _deepSeekService;
        private readonly DatabaseService _databaseService;
        // private readonly TaskSchedulerService _taskSchedulerService;
        private readonly ILogger<AutomationController> _logger;

        public AutomationController(
            ChromeService chromeService,
            WebScrapingService webScrapingService,
            WeChatService weChatService,
            NewsScrapingService newsScrapingService,
            DeepSeekService deepSeekService,
            DatabaseService databaseService,
            // TaskSchedulerService taskSchedulerService,
            ILogger<AutomationController> logger)
        {
            _chromeService = chromeService;
            _webScrapingService = webScrapingService;
            _weChatService = weChatService;
            _newsScrapingService = newsScrapingService;
            _deepSeekService = deepSeekService;
            _databaseService = databaseService;
            // _taskSchedulerService = taskSchedulerService;
            _logger = logger;
        }

        #region Chrome控制

        [HttpPost("chrome/init")]
        public async Task<IActionResult> InitializeChrome([FromBody] ChromeOptions? options = null)
        {
            try
            {
                var success = await _chromeService.InitializeBrowserAsync(options);
                var result = new { message = success ? "Chrome浏览器初始化成功" : "Chrome浏览器初始化失败", success = success };
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "Chrome初始化",
                    Details = $"选项: {System.Text.Json.JsonSerializer.Serialize(options)}",
                    Success = success
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Chrome浏览器时发生错误");
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "Chrome初始化",
                    Details = "初始化失败",
                    Success = false,
                    ErrorMessage = ex.Message
                });
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("chrome/navigate")]
        public async Task<IActionResult> NavigateToUrl([FromBody] NavigateRequest request)
        {
            try
            {
                var success = await _chromeService.NavigateToAsync(request.Url);
                var title = success ? _chromeService.GetPageTitle() : "";
                var currentUrl = success ? _chromeService.GetCurrentUrl() : "";
                
                var result = new 
                { 
                    message = success ? "导航成功" : "导航失败", 
                    success = success, 
                    title = title,
                    currentUrl = currentUrl
                };

                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "页面导航",
                    Details = $"URL: {request.Url}",
                    Success = success
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("chrome/click")]
        public async Task<IActionResult> ClickElement([FromBody] ElementActionRequest request)
        {
            try
            {
                var success = await _chromeService.ClickElementAsync(request.Selector, request.TimeoutSeconds);
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "元素点击",
                    Details = $"选择器: {request.Selector}",
                    Success = success
                });

                return Ok(new { message = success ? "点击成功" : "点击失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "点击元素时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("chrome/input")]
        public async Task<IActionResult> InputText([FromBody] InputTextRequest request)
        {
            try
            {
                var success = await _chromeService.InputTextAsync(request.Selector, request.Text, request.TimeoutSeconds);
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "文本输入",
                    Details = $"选择器: {request.Selector}, 文本: {request.Text}",
                    Success = success
                });

                return Ok(new { message = success ? "输入成功" : "输入失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "输入文本时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("chrome/screenshot")]
        public async Task<IActionResult> TakeScreenshot()
        {
            try
            {
                var screenshot = await _chromeService.TakeScreenshotAsync();
                if (screenshot != null)
                {
                    await _databaseService.AddOperationLogAsync(new OperationLog
                    {
                        Operation = "页面截图",
                        Details = "截图成功",
                        Success = true
                    });

                    return File(screenshot, "image/png", "screenshot.png");
                }
                else
                {
                    return BadRequest(new { message = "截图失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "截图时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region 网页爬虫

        [HttpPost("scraping/webpage")]
        public async Task<IActionResult> ScrapeWebPage([FromBody] ScrapingRequest request)
        {
            try
            {
                var result = await _webScrapingService.ScrapeWebPageAsync(request);
                
                if (request.GenerateSummary && !string.IsNullOrEmpty(result.Content))
                {
                    result.Summary = await _deepSeekService.GenerateSummaryAsync(result.Content);
                }

                await _databaseService.SaveScrapingResultAsync(result);
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "网页爬取",
                    Details = $"URL: {request.Url}",
                    Success = result.IsSuccess,
                    ErrorMessage = result.ErrorMessage
                });
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取网页时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("scraping/results")]
        public async Task<IActionResult> GetScrapingResults([FromQuery] int count = 50)
        {
            try
            {
                var results = await _databaseService.GetScrapingResultsAsync(count);
                return Ok(new { results = results, count = results.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取爬虫结果时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region 微信操作

        [HttpPost("wechat/login")]
        public async Task<IActionResult> WeChatLogin()
        {
            try
            {
                var success = await _weChatService.LoginAsync();
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "微信登录",
                    Details = "尝试登录微信网页版",
                    Success = success
                });

                return Ok(new { message = success ? "微信登录成功" : "微信登录失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登录时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("wechat/chats")]
        public async Task<IActionResult> GetWeChatChats()
        {
            try
            {
                var chats = await _weChatService.GetChatListAsync();
                return Ok(new { chats = chats, count = chats.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取微信聊天列表时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("wechat/messages")]
        public async Task<IActionResult> GetWeChatMessages([FromQuery] int maxCount = 50)
        {
            try
            {
                var messages = await _weChatService.GetMessagesAsync(maxCount);
                return Ok(new { messages = messages, count = messages.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取微信消息时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("wechat/analyze")]
        public async Task<IActionResult> AnalyzeWeChatMessages([FromBody] List<WeChatMessage> messages)
        {
            try
            {
                var analysis = await _deepSeekService.AnalyzeWeChatMessagesAsync(messages);
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "微信消息分析",
                    Details = $"分析了 {messages.Count} 条消息",
                    Success = !string.IsNullOrEmpty(analysis)
                });

                return Ok(new { analysis = analysis, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析微信消息时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region 新闻爬虫

        [HttpGet("news/sources")]
        public IActionResult GetNewsSources()
        {
            try
            {
                var sources = _newsScrapingService.GetSupportedNewsSources();
                return Ok(new { sources = sources, count = sources.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取新闻源时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("news/{source}")]
        public async Task<IActionResult> ScrapeNewsList(string source, [FromQuery] int maxArticles = 10)
        {
            try
            {
                var articles = await _newsScrapingService.ScrapeNewsListAsync(source, maxArticles);
                
                if (articles.Any())
                {
                    await _databaseService.SaveNewsArticlesAsync(articles);
                }

                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "新闻爬取",
                    Details = $"来源: {source}, 获取 {articles.Count} 篇文章",
                    Success = true
                });

                return Ok(new { articles = articles, count = articles.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取新闻列表时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("news/article")]
        public async Task<IActionResult> ScrapeNewsArticle([FromBody] NewsArticleRequest request)
        {
            try
            {
                var article = await _newsScrapingService.ScrapeNewsArticleAsync(request.Url, request.Source);
                if (article != null)
                {
                    await _databaseService.SaveNewsArticlesAsync(new List<NewsArticle> { article });
                    return Ok(new { article = article, success = true });
                }
                else
                {
                    return BadRequest(new { message = "爬取文章失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取新闻文章时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("news/batch")]
        public async Task<IActionResult> ScrapeMultipleSources([FromBody] BatchNewsRequest request)
        {
            try
            {
                var articles = await _newsScrapingService.ScrapeMultipleSourcesAsync(request.Sources, request.ArticlesPerSource);
                
                if (articles.Any())
                {
                    await _databaseService.SaveNewsArticlesAsync(articles);
                }

                return Ok(new { articles = articles, count = articles.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量爬取新闻时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("news/articles")]
        public async Task<IActionResult> GetNewsArticles([FromQuery] int count = 50, [FromQuery] string? source = null)
        {
            try
            {
                var articles = await _databaseService.GetNewsArticlesAsync(count, source);
                return Ok(new { articles = articles, count = articles.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取新闻文章时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region AI分析

        [HttpPost("ai/summary")]
        public async Task<IActionResult> GenerateSummary([FromBody] SummaryRequest request)
        {
            try
            {
                var summary = await _deepSeekService.GenerateSummaryAsync(request.Content, request.CustomPrompt);
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "AI摘要生成",
                    Details = $"内容长度: {request.Content.Length} 字符",
                    Success = !string.IsNullOrEmpty(summary)
                });

                return Ok(new { summary = summary, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成摘要时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("ai/status")]
        public async Task<IActionResult> CheckAIStatus()
        {
            try
            {
                var isOnline = await _deepSeekService.CheckApiStatusAsync();
                return Ok(new { isOnline = isOnline, message = isOnline ? "API正常" : "API离线", success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查AI状态时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region 系统管理

        [HttpGet("system/status")]
        public async Task<IActionResult> GetSystemStatus()
        {
            try
            {
                var status = await _databaseService.GetSystemStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统状态时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpGet("system/logs")]
        public async Task<IActionResult> GetOperationLogs([FromQuery] int count = 100)
        {
            try
            {
                var logs = await _databaseService.GetOperationLogsAsync(count);
                return Ok(new { logs = logs, count = logs.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取操作日志时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("system/cleanup")]
        public async Task<IActionResult> CleanupOldData()
        {
            try
            {
                await _databaseService.CleanupOldDataAsync();
                
                await _databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "数据清理",
                    Details = "清理30天前的旧数据",
                    Success = true
                });

                return Ok(new { message = "数据清理完成", success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理数据时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        #endregion

        #region 任务调度 (暂时禁用)

        /*
        [HttpGet("tasks")]
        public async Task<IActionResult> GetScheduledTasks()
        {
            try
            {
                var tasks = await _taskSchedulerService.GetScheduledTasksAsync();
                return Ok(new { tasks = tasks, count = tasks.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取定时任务时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("tasks")]
        public async Task<IActionResult> AddScheduledTask([FromBody] ScheduledTask task)
        {
            try
            {
                var success = await _taskSchedulerService.AddScheduledTaskAsync(task);
                return Ok(new { message = success ? "任务添加成功" : "任务添加失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加定时任务时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpDelete("tasks/{taskId}")]
        public async Task<IActionResult> RemoveScheduledTask(string taskId)
        {
            try
            {
                var success = await _taskSchedulerService.RemoveScheduledTaskAsync(taskId);
                return Ok(new { message = success ? "任务删除成功" : "任务删除失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除定时任务时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("tasks/{taskId}/toggle")]
        public async Task<IActionResult> ToggleTask(string taskId, [FromBody] bool enabled)
        {
            try
            {
                var success = await _taskSchedulerService.ToggleTaskAsync(taskId, enabled);
                return Ok(new { message = success ? "任务状态更新成功" : "任务状态更新失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        [HttpPost("tasks/{taskId}/execute")]
        public async Task<IActionResult> ExecuteTaskNow(string taskId)
        {
            try
            {
                var success = await _taskSchedulerService.ExecuteTaskNowAsync(taskId);
                return Ok(new { message = success ? "任务执行成功" : "任务执行失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行任务时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }
        */

        #endregion
    }
}
