using Microsoft.AspNetCore.Mvc;
using WebAutomation.Models;
using WebAutomation.Services;

namespace WebAutomation.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AutomationController : ControllerBase
    {
        private readonly ChromeService _chromeService;
        private readonly WebScrapingService _webScrapingService;
        private readonly WeChatService _weChatService;
        private readonly NewsScrapingService _newsScrapingService;
        private readonly DeepSeekService _deepSeekService;
        private readonly ILogger<AutomationController> _logger;

        public AutomationController(
            ChromeService chromeService,
            WebScrapingService webScrapingService,
            WeChatService weChatService,
            NewsScrapingService newsScrapingService,
            DeepSeekService deepSeekService,
            ILogger<AutomationController> logger)
        {
            _chromeService = chromeService;
            _webScrapingService = webScrapingService;
            _weChatService = weChatService;
            _newsScrapingService = newsScrapingService;
            _deepSeekService = deepSeekService;
            _logger = logger;
        }

        /// <summary>
        /// 初始化Chrome浏览器
        /// </summary>
        [HttpPost("chrome/init")]
        public async Task<IActionResult> InitializeChrome([FromBody] Models.ChromeOptions? options = null)
        {
            try
            {
                var success = await _chromeService.InitializeBrowserAsync(options);
                if (success)
                {
                    return Ok(new { message = "Chrome浏览器初始化成功", success = true });
                }
                else
                {
                    return BadRequest(new { message = "Chrome浏览器初始化失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Chrome浏览器时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        [HttpPost("chrome/navigate")]
        public async Task<IActionResult> NavigateToUrl([FromBody] NavigateRequest request)
        {
            try
            {
                var success = await _chromeService.NavigateToAsync(request.Url);
                if (success)
                {
                    var title = _chromeService.GetPageTitle();
                    var currentUrl = _chromeService.GetCurrentUrl();
                    
                    return Ok(new 
                    { 
                        message = "导航成功", 
                        success = true, 
                        title = title,
                        currentUrl = currentUrl
                    });
                }
                else
                {
                    return BadRequest(new { message = "导航失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 点击页面元素
        /// </summary>
        [HttpPost("chrome/click")]
        public async Task<IActionResult> ClickElement([FromBody] ElementActionRequest request)
        {
            try
            {
                var success = await _chromeService.ClickElementAsync(request.Selector, request.TimeoutSeconds);
                return Ok(new { message = success ? "点击成功" : "点击失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "点击元素时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 输入文本
        /// </summary>
        [HttpPost("chrome/input")]
        public async Task<IActionResult> InputText([FromBody] InputTextRequest request)
        {
            try
            {
                var success = await _chromeService.InputTextAsync(request.Selector, request.Text, request.TimeoutSeconds);
                return Ok(new { message = success ? "输入成功" : "输入失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "输入文本时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 获取页面截图
        /// </summary>
        [HttpGet("chrome/screenshot")]
        public async Task<IActionResult> TakeScreenshot()
        {
            try
            {
                var screenshot = await _chromeService.TakeScreenshotAsync();
                if (screenshot != null)
                {
                    return File(screenshot, "image/png", "screenshot.png");
                }
                else
                {
                    return BadRequest(new { message = "截图失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "截图时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 爬取网页内容
        /// </summary>
        [HttpPost("scraping/webpage")]
        public async Task<IActionResult> ScrapeWebPage([FromBody] ScrapingRequest request)
        {
            try
            {
                var result = await _webScrapingService.ScrapeWebPageAsync(request);
                
                // 如果需要生成摘要
                if (request.GenerateSummary && !string.IsNullOrEmpty(result.Content))
                {
                    result.Summary = await _deepSeekService.GenerateSummaryAsync(result.Content);
                }
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取网页时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 微信登录
        /// </summary>
        [HttpPost("wechat/login")]
        public async Task<IActionResult> WeChatLogin()
        {
            try
            {
                var success = await _weChatService.LoginAsync();
                return Ok(new { message = success ? "微信登录成功" : "微信登录失败", success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登录时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 获取微信聊天列表
        /// </summary>
        [HttpGet("wechat/chats")]
        public async Task<IActionResult> GetWeChatChats()
        {
            try
            {
                var chats = await _weChatService.GetChatListAsync();
                return Ok(new { chats = chats, count = chats.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取微信聊天列表时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 获取微信消息
        /// </summary>
        [HttpGet("wechat/messages")]
        public async Task<IActionResult> GetWeChatMessages([FromQuery] int maxCount = 50)
        {
            try
            {
                var messages = await _weChatService.GetMessagesAsync(maxCount);
                return Ok(new { messages = messages, count = messages.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取微信消息时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 分析微信消息
        /// </summary>
        [HttpPost("wechat/analyze")]
        public async Task<IActionResult> AnalyzeWeChatMessages([FromBody] List<WeChatMessage> messages)
        {
            try
            {
                var analysis = await _deepSeekService.AnalyzeWeChatMessagesAsync(messages);
                return Ok(new { analysis = analysis, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析微信消息时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 获取支持的新闻源
        /// </summary>
        [HttpGet("news/sources")]
        public IActionResult GetNewsSources()
        {
            try
            {
                var sources = _newsScrapingService.GetSupportedNewsSources();
                return Ok(new { sources = sources, count = sources.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取新闻源时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 爬取新闻列表
        /// </summary>
        [HttpGet("news/{source}")]
        public async Task<IActionResult> ScrapeNewsList(string source, [FromQuery] int maxArticles = 10)
        {
            try
            {
                var articles = await _newsScrapingService.ScrapeNewsListAsync(source, maxArticles);
                return Ok(new { articles = articles, count = articles.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取新闻列表时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 爬取单篇新闻文章
        /// </summary>
        [HttpPost("news/article")]
        public async Task<IActionResult> ScrapeNewsArticle([FromBody] NewsArticleRequest request)
        {
            try
            {
                var article = await _newsScrapingService.ScrapeNewsArticleAsync(request.Url, request.Source);
                if (article != null)
                {
                    return Ok(new { article = article, success = true });
                }
                else
                {
                    return BadRequest(new { message = "爬取文章失败", success = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取新闻文章时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 批量爬取多个新闻源
        /// </summary>
        [HttpPost("news/batch")]
        public async Task<IActionResult> ScrapeMultipleSources([FromBody] BatchNewsRequest request)
        {
            try
            {
                var articles = await _newsScrapingService.ScrapeMultipleSourcesAsync(request.Sources, request.ArticlesPerSource);
                return Ok(new { articles = articles, count = articles.Count, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量爬取新闻时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 生成内容摘要
        /// </summary>
        [HttpPost("ai/summary")]
        public async Task<IActionResult> GenerateSummary([FromBody] SummaryRequest request)
        {
            try
            {
                var summary = await _deepSeekService.GenerateSummaryAsync(request.Content, request.CustomPrompt);
                return Ok(new { summary = summary, success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成摘要时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }

        /// <summary>
        /// 检查DeepSeek API状态
        /// </summary>
        [HttpGet("ai/status")]
        public async Task<IActionResult> CheckAIStatus()
        {
            try
            {
                var isOnline = await _deepSeekService.CheckApiStatusAsync();
                return Ok(new { isOnline = isOnline, message = isOnline ? "API正常" : "API离线", success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查AI状态时发生错误");
                return StatusCode(500, new { message = ex.Message, success = false });
            }
        }
    }

    // 请求模型
    public class NavigateRequest
    {
        public string Url { get; set; } = string.Empty;
    }

    public class ElementActionRequest
    {
        public string Selector { get; set; } = string.Empty;
        public int TimeoutSeconds { get; set; } = 10;
    }

    public class InputTextRequest
    {
        public string Selector { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public int TimeoutSeconds { get; set; } = 10;
    }

    public class NewsArticleRequest
    {
        public string Url { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
    }

    public class BatchNewsRequest
    {
        public List<string> Sources { get; set; } = new List<string>();
        public int ArticlesPerSource { get; set; } = 5;
    }

    public class SummaryRequest
    {
        public string Content { get; set; } = string.Empty;
        public string? CustomPrompt { get; set; }
    }
}
