is_global = true
build_property.TargetFramework = net8.0
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WebAutomation
build_property.RootNamespace = WebAutomation
build_property.ProjectDir = C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/About.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/AI.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQUkucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Chrome.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ2hyb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Logs.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTG9ncy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/News.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTmV3cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Scraping.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2NyYXBpbmcucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Settings.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2V0dGluZ3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Test.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/WeChat.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcV2VDaGF0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Shared/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/csharpChrome/WebAutomation/Pages/_Host.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0hvc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 
