# WebAutomation API 演示脚本
# 这个脚本演示了如何使用WebAutomation的各种功能

$baseUrl = "http://localhost:5096/api/automation"

Write-Host "🚀 WebAutomation API 演示开始" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 1. 检查API状态
Write-Host "`n📊 检查API状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/../health" -Method GET
    Write-Host "✅ API状态: $($response.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ API连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 初始化Chrome浏览器
Write-Host "`n🌐 初始化Chrome浏览器..." -ForegroundColor Yellow
$chromeOptions = @{
    headless = $false
    disableImages = $false
    windowWidth = 1280
    windowHeight = 720
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/chrome/init" -Method POST -Body ($chromeOptions | ConvertTo-Json) -ContentType "application/json"
    Write-Host "✅ Chrome初始化成功: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ Chrome初始化失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 导航到示例网站
Write-Host "`n🔗 导航到示例网站..." -ForegroundColor Yellow
$navigateRequest = @{
    url = "https://example.com"
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/chrome/navigate" -Method POST -Body ($navigateRequest | ConvertTo-Json) -ContentType "application/json"
    Write-Host "✅ 导航成功: $($response.title)" -ForegroundColor Green
} catch {
    Write-Host "❌ 导航失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 爬取网页内容
Write-Host "`n🕷️ 爬取网页内容..." -ForegroundColor Yellow
$scrapeRequest = @{
    url = "https://example.com"
    includeImages = $true
    includeLinks = $true
    generateSummary = $false
    waitTimeSeconds = 3
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/scraping/webpage" -Method POST -Body ($scrapeRequest | ConvertTo-Json) -ContentType "application/json"
    Write-Host "✅ 爬取成功!" -ForegroundColor Green
    Write-Host "   标题: $($response.title)" -ForegroundColor Cyan
    Write-Host "   内容长度: $($response.content.Length) 字符" -ForegroundColor Cyan
    Write-Host "   图片数量: $($response.images.Count)" -ForegroundColor Cyan
    Write-Host "   链接数量: $($response.links.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 爬取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 获取支持的新闻源
Write-Host "`n📰 获取支持的新闻源..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/news/sources" -Method GET
    Write-Host "✅ 支持的新闻源:" -ForegroundColor Green
    foreach ($source in $response.sources) {
        Write-Host "   - $source" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ 获取新闻源失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 检查AI服务状态
Write-Host "`n🤖 检查AI服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/ai/status" -Method GET
    if ($response.isOnline) {
        Write-Host "✅ AI服务在线" -ForegroundColor Green
    } else {
        Write-Host "⚠️ AI服务离线 (需要配置DeepSeek API密钥)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ AI服务检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 演示AI摘要功能（如果AI服务可用）
Write-Host "`n📝 测试AI摘要功能..." -ForegroundColor Yellow
$summaryRequest = @{
    content = "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这包括学习、推理、问题解决、感知和语言理解。近年来，机器学习和深度学习的发展推动了AI技术的快速进步，使其在图像识别、自然语言处理、自动驾驶等领域取得了显著成果。"
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/ai/summary" -Method POST -Body ($summaryRequest | ConvertTo-Json) -ContentType "application/json"
    Write-Host "✅ AI摘要生成成功:" -ForegroundColor Green
    Write-Host "$($response.summary)" -ForegroundColor Cyan
} catch {
    Write-Host "⚠️ AI摘要功能需要配置DeepSeek API密钥" -ForegroundColor Yellow
}

# 8. 截图功能演示
Write-Host "`n📸 获取网页截图..." -ForegroundColor Yellow
try {
    $screenshotPath = "screenshot.png"
    Invoke-RestMethod -Uri "$baseUrl/chrome/screenshot" -Method GET -OutFile $screenshotPath
    Write-Host "✅ 截图已保存到: $screenshotPath" -ForegroundColor Green
} catch {
    Write-Host "❌ 截图失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 演示完成!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "💡 提示:" -ForegroundColor Yellow
Write-Host "1. 访问 http://localhost:5096 查看Web控制台" -ForegroundColor Cyan
Write-Host "2. 访问 http://localhost:5096/swagger 查看API文档" -ForegroundColor Cyan
Write-Host "3. 在 appsettings.json 中配置DeepSeek API密钥以启用AI功能" -ForegroundColor Cyan
Write-Host "4. 确保Chrome浏览器已安装以使用自动化功能" -ForegroundColor Cyan

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
