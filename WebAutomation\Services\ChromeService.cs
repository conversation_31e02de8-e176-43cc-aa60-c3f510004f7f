using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    /// <summary>
    /// Chrome浏览器自动化服务
    /// </summary>
    public class ChromeService : IDisposable
    {
        private ChromeDriver? _driver;
        private readonly ILogger<ChromeService> _logger;
        private bool _disposed = false;

        public ChromeService(ILogger<ChromeService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 初始化浏览器
        /// </summary>
        public async Task<bool> InitializeBrowserAsync(Models.ChromeOptions? options = null)
        {
            try
            {
                await Task.Run(() =>
                {
                    var chromeOptions = new OpenQA.Selenium.Chrome.ChromeOptions();
                    
                    if (options != null)
                    {
                        if (options.Headless)
                            chromeOptions.AddArgument("--headless");
                        
                        if (options.DisableImages)
                        {
                            chromeOptions.AddArgument("--disable-images");
                            chromeOptions.AddUserProfilePreference("profile.managed_default_content_settings.images", 2);
                        }
                        
                        if (options.DisableJavaScript)
                            chromeOptions.AddUserProfilePreference("profile.managed_default_content_settings.javascript", 2);
                        
                        if (!string.IsNullOrEmpty(options.UserAgent))
                            chromeOptions.AddArgument($"--user-agent={options.UserAgent}");
                        
                        if (!string.IsNullOrEmpty(options.ProxyServer))
                            chromeOptions.AddArgument($"--proxy-server={options.ProxyServer}");
                        
                        chromeOptions.AddArgument($"--window-size={options.WindowWidth},{options.WindowHeight}");
                    }
                    
                    // 通用设置
                    chromeOptions.AddArgument("--no-sandbox");
                    chromeOptions.AddArgument("--disable-dev-shm-usage");
                    chromeOptions.AddArgument("--disable-gpu");
                    chromeOptions.AddArgument("--disable-web-security");
                    chromeOptions.AddArgument("--allow-running-insecure-content");
                    
                    _driver = new ChromeDriver(chromeOptions);
                    _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(options?.PageLoadTimeout ?? 30);
                    _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(10);
                });

                _logger.LogInformation("Chrome浏览器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Chrome浏览器初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        public async Task<bool> NavigateToAsync(string url)
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return false;
            }

            try
            {
                await Task.Run(() => _driver.Navigate().GoToUrl(url));
                _logger.LogInformation("成功导航到: {Url}", url);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航失败: {Url}", url);
                return false;
            }
        }

        /// <summary>
        /// 点击元素
        /// </summary>
        public async Task<bool> ClickElementAsync(string selector, int timeoutSeconds = 10)
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return false;
            }

            try
            {
                await Task.Run(() =>
                {
                    var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                    var element = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                    element.Click();
                });

                _logger.LogInformation("成功点击元素: {Selector}", selector);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "点击元素失败: {Selector}", selector);
                return false;
            }
        }

        /// <summary>
        /// 输入文本
        /// </summary>
        public async Task<bool> InputTextAsync(string selector, string text, int timeoutSeconds = 10)
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return false;
            }

            try
            {
                await Task.Run(() =>
                {
                    var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                    var element = wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                    element.Clear();
                    element.SendKeys(text);
                });

                _logger.LogInformation("成功输入文本到元素: {Selector}", selector);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "输入文本失败: {Selector}", selector);
                return false;
            }
        }

        /// <summary>
        /// 获取页面截图
        /// </summary>
        public async Task<byte[]?> TakeScreenshotAsync()
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return null;
            }

            try
            {
                return await Task.Run(() =>
                {
                    var screenshot = ((ITakesScreenshot)_driver).GetScreenshot();
                    return screenshot.AsByteArray;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "截图失败");
                return null;
            }
        }

        /// <summary>
        /// 获取页面标题
        /// </summary>
        public string GetPageTitle()
        {
            return _driver?.Title ?? "";
        }

        /// <summary>
        /// 获取当前URL
        /// </summary>
        public string GetCurrentUrl()
        {
            return _driver?.Url ?? "";
        }

        /// <summary>
        /// 执行JavaScript
        /// </summary>
        public async Task<object?> ExecuteJavaScriptAsync(string script)
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return null;
            }

            try
            {
                return await Task.Run(() =>
                {
                    var jsExecutor = (IJavaScriptExecutor)_driver;
                    return jsExecutor.ExecuteScript(script);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行JavaScript失败");
                return null;
            }
        }

        /// <summary>
        /// 等待元素出现
        /// </summary>
        public async Task<IWebElement?> WaitForElementAsync(string selector, int timeoutSeconds = 10)
        {
            if (_driver == null)
            {
                _logger.LogError("浏览器未初始化");
                return null;
            }

            try
            {
                return await Task.Run(() =>
                {
                    var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeoutSeconds));
                    return wait.Until(driver => driver.FindElement(By.CssSelector(selector)));
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "等待元素失败: {Selector}", selector);
                return null;
            }
        }

        /// <summary>
        /// 获取元素文本
        /// </summary>
        public async Task<string> GetElementTextAsync(string selector, int timeoutSeconds = 10)
        {
            try
            {
                var element = await WaitForElementAsync(selector, timeoutSeconds);
                return element?.Text ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取元素文本失败: {Selector}", selector);
                return "";
            }
        }

        /// <summary>
        /// 检查元素是否存在
        /// </summary>
        public async Task<bool> ElementExistsAsync(string selector)
        {
            if (_driver == null)
                return false;

            try
            {
                return await Task.Run(() =>
                {
                    var elements = _driver.FindElements(By.CssSelector(selector));
                    return elements.Count > 0;
                });
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 关闭浏览器
        /// </summary>
        public void CloseBrowser()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
                _driver = null;
                _logger.LogInformation("Chrome浏览器已关闭");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭浏览器时发生错误");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                CloseBrowser();
                _disposed = true;
            }
        }
    }
}
