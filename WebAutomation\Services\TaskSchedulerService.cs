using Quartz;
using WebAutomation.Models;
using WebAutomation.Data;

namespace WebAutomation.Services
{
    /// <summary>
    /// 任务调度服务
    /// </summary>
    public class TaskSchedulerService
    {
        private readonly ISchedulerFactory _schedulerFactory;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<TaskSchedulerService> _logger;
        private IScheduler? _scheduler;

        public TaskSchedulerService(
            ISchedulerFactory schedulerFactory,
            DatabaseService databaseService,
            ILogger<TaskSchedulerService> logger)
        {
            _schedulerFactory = schedulerFactory;
            _databaseService = databaseService;
            _logger = logger;
        }

        /// <summary>
        /// 启动调度器
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                _scheduler = await _schedulerFactory.GetScheduler();
                await _scheduler.Start();
                
                // 加载并启动所有启用的任务
                await LoadAndStartTasksAsync();
                
                _logger.LogInformation("任务调度器启动成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "任务调度器启动失败");
                throw;
            }
        }

        /// <summary>
        /// 停止调度器
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                if (_scheduler != null)
                {
                    await _scheduler.Shutdown();
                    _logger.LogInformation("任务调度器已停止");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止任务调度器失败");
            }
        }

        /// <summary>
        /// 添加定时任务
        /// </summary>
        public async Task<bool> AddScheduledTaskAsync(ScheduledTask task)
        {
            try
            {
                if (_scheduler == null)
                {
                    _logger.LogError("调度器未初始化");
                    return false;
                }

                // 创建作业
                var jobDetail = JobBuilder.Create<ScheduledTaskJob>()
                    .WithIdentity(task.Id, "WebAutomation")
                    .UsingJobData("TaskId", task.Id)
                    .UsingJobData("TaskType", task.Type)
                    .UsingJobData("Parameters", System.Text.Json.JsonSerializer.Serialize(task.Parameters))
                    .Build();

                // 创建触发器
                var trigger = TriggerBuilder.Create()
                    .WithIdentity($"{task.Id}_trigger", "WebAutomation")
                    .WithCronSchedule(task.CronExpression)
                    .Build();

                // 调度作业
                await _scheduler.ScheduleJob(jobDetail, trigger);

                // 保存到数据库
                var tasks = await _databaseService.LoadAsync<List<ScheduledTask>>("scheduled_tasks.json") ?? new List<ScheduledTask>();
                var existingIndex = tasks.FindIndex(t => t.Id == task.Id);
                if (existingIndex >= 0)
                {
                    tasks[existingIndex] = task;
                }
                else
                {
                    tasks.Add(task);
                }
                await _databaseService.SaveAsync("scheduled_tasks.json", tasks);

                _logger.LogInformation("定时任务添加成功: {TaskName}", task.Name);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加定时任务失败: {TaskName}", task.Name);
                return false;
            }
        }

        /// <summary>
        /// 删除定时任务
        /// </summary>
        public async Task<bool> RemoveScheduledTaskAsync(string taskId)
        {
            try
            {
                if (_scheduler == null)
                {
                    _logger.LogError("调度器未初始化");
                    return false;
                }

                // 从调度器中删除
                var jobKey = new JobKey(taskId, "WebAutomation");
                await _scheduler.DeleteJob(jobKey);

                // 从数据库中删除
                var tasks = await _databaseService.LoadAsync<List<ScheduledTask>>("scheduled_tasks.json") ?? new List<ScheduledTask>();
                tasks.RemoveAll(t => t.Id == taskId);
                await _databaseService.SaveAsync("scheduled_tasks.json", tasks);

                _logger.LogInformation("定时任务删除成功: {TaskId}", taskId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除定时任务失败: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 获取所有定时任务
        /// </summary>
        public async Task<List<ScheduledTask>> GetScheduledTasksAsync()
        {
            try
            {
                return await _databaseService.LoadAsync<List<ScheduledTask>>("scheduled_tasks.json") ?? new List<ScheduledTask>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取定时任务失败");
                return new List<ScheduledTask>();
            }
        }

        /// <summary>
        /// 启用/禁用任务
        /// </summary>
        public async Task<bool> ToggleTaskAsync(string taskId, bool enabled)
        {
            try
            {
                var tasks = await _databaseService.LoadAsync<List<ScheduledTask>>("scheduled_tasks.json") ?? new List<ScheduledTask>();
                var task = tasks.FirstOrDefault(t => t.Id == taskId);
                
                if (task == null)
                {
                    _logger.LogError("任务不存在: {TaskId}", taskId);
                    return false;
                }

                task.IsEnabled = enabled;
                await _databaseService.SaveAsync("scheduled_tasks.json", tasks);

                if (_scheduler != null)
                {
                    var jobKey = new JobKey(taskId, "WebAutomation");
                    if (enabled)
                    {
                        await _scheduler.ResumeJob(jobKey);
                    }
                    else
                    {
                        await _scheduler.PauseJob(jobKey);
                    }
                }

                _logger.LogInformation("任务状态更新成功: {TaskId} -> {Enabled}", taskId, enabled);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态失败: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 立即执行任务
        /// </summary>
        public async Task<bool> ExecuteTaskNowAsync(string taskId)
        {
            try
            {
                if (_scheduler == null)
                {
                    _logger.LogError("调度器未初始化");
                    return false;
                }

                var jobKey = new JobKey(taskId, "WebAutomation");
                await _scheduler.TriggerJob(jobKey);

                _logger.LogInformation("任务立即执行: {TaskId}", taskId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "立即执行任务失败: {TaskId}", taskId);
                return false;
            }
        }

        /// <summary>
        /// 加载并启动所有任务
        /// </summary>
        private async Task LoadAndStartTasksAsync()
        {
            try
            {
                var tasks = await GetScheduledTasksAsync();
                foreach (var task in tasks.Where(t => t.IsEnabled))
                {
                    await AddScheduledTaskAsync(task);
                }
                
                _logger.LogInformation("加载了 {Count} 个定时任务", tasks.Count(t => t.IsEnabled));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载定时任务失败");
            }
        }
    }

    /// <summary>
    /// 定时任务作业
    /// </summary>
    public class ScheduledTaskJob : IJob
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ScheduledTaskJob> _logger;

        public ScheduledTaskJob(IServiceProvider serviceProvider, ILogger<ScheduledTaskJob> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var taskId = context.JobDetail.JobDataMap.GetString("TaskId");
            var taskType = context.JobDetail.JobDataMap.GetString("TaskType");
            var parametersJson = context.JobDetail.JobDataMap.GetString("Parameters");

            try
            {
                _logger.LogInformation("开始执行定时任务: {TaskId}, 类型: {TaskType}", taskId, taskType);

                using var scope = _serviceProvider.CreateScope();
                var databaseService = scope.ServiceProvider.GetRequiredService<DatabaseService>();

                // 更新任务的最后执行时间
                var tasks = await databaseService.LoadAsync<List<ScheduledTask>>("scheduled_tasks.json") ?? new List<ScheduledTask>();
                var task = tasks.FirstOrDefault(t => t.Id == taskId);
                if (task != null)
                {
                    task.LastRun = DateTime.UtcNow;
                    // task.NextRun = context.NextFireTime?.UtcDateTime;
                    await databaseService.SaveAsync("scheduled_tasks.json", tasks);
                }

                // 根据任务类型执行相应的操作
                switch (taskType?.ToLower())
                {
                    case "scraping":
                        await ExecuteScrapingTaskAsync(scope, parametersJson);
                        break;
                    case "news":
                        await ExecuteNewsTaskAsync(scope, parametersJson);
                        break;
                    case "wechat":
                        await ExecuteWeChatTaskAsync(scope, parametersJson);
                        break;
                    default:
                        _logger.LogWarning("未知的任务类型: {TaskType}", taskType);
                        break;
                }

                // 记录操作日志
                await databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "定时任务执行",
                    Details = $"任务ID: {taskId}, 类型: {taskType}",
                    Success = true
                });

                _logger.LogInformation("定时任务执行完成: {TaskId}", taskId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时任务执行失败: {TaskId}", taskId);
                
                // 记录错误日志
                using var scope = _serviceProvider.CreateScope();
                var databaseService = scope.ServiceProvider.GetRequiredService<DatabaseService>();
                await databaseService.AddOperationLogAsync(new OperationLog
                {
                    Operation = "定时任务执行",
                    Details = $"任务ID: {taskId}, 类型: {taskType}",
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task ExecuteScrapingTaskAsync(IServiceScope scope, string? parametersJson)
        {
            var scrapingService = scope.ServiceProvider.GetRequiredService<WebScrapingService>();
            // 实现爬虫任务逻辑
            await Task.Delay(1000); // 占位符
        }

        private async Task ExecuteNewsTaskAsync(IServiceScope scope, string? parametersJson)
        {
            var newsService = scope.ServiceProvider.GetRequiredService<NewsScrapingService>();
            // 实现新闻爬取任务逻辑
            await Task.Delay(1000); // 占位符
        }

        private async Task ExecuteWeChatTaskAsync(IServiceScope scope, string? parametersJson)
        {
            var wechatService = scope.ServiceProvider.GetRequiredService<WeChatService>();
            // 实现微信任务逻辑
            await Task.Delay(1000); // 占位符
        }
    }
}
