# WebAutomation 安装配置指南

## 🚀 快速开始

### 1. 环境准备

#### 必需软件
- ✅ **.NET 8.0 SDK** 或更高版本
- ✅ **Chrome浏览器** (最新版本)
- ✅ **Visual Studio 2022** 或 **VS Code** (推荐)

#### 检查环境
```bash
# 检查.NET版本
dotnet --version

# 检查Chrome是否安装
chrome --version
```

### 2. 项目安装

#### 克隆或下载项目
```bash
# 如果有Git仓库
git clone <repository-url>
cd WebAutomation

# 或者直接下载解压到目录
```

#### 安装依赖
```bash
# 进入项目目录
cd WebAutomation

# 还原NuGet包
dotnet restore

# 构建项目
dotnet build
```

### 3. 配置设置

#### 3.1 DeepSeek API配置 (可选但推荐)

1. 访问 [DeepSeek官网](https://platform.deepseek.com/) 注册账号
2. 获取API密钥
3. 编辑 `appsettings.json` 文件：

```json
{
  "DeepSeek": {
    "ApiKey": "YOUR_DEEPSEEK_API_KEY_HERE",
    "BaseUrl": "https://api.deepseek.com/v1"
  }
}
```

#### 3.2 Chrome配置 (可选)

在 `appsettings.json` 中调整Chrome设置：

```json
{
  "Chrome": {
    "DefaultHeadless": false,
    "DefaultWindowWidth": 1920,
    "DefaultWindowHeight": 1080,
    "DefaultPageLoadTimeout": 30
  }
}
```

#### 3.3 爬虫配置 (可选)

```json
{
  "WebScraping": {
    "DefaultWaitTime": 3,
    "MaxRetries": 3,
    "RequestDelay": 1000
  }
}
```

### 4. 运行项目

#### 4.1 开发模式运行
```bash
# 默认端口运行
dotnet run

# 指定端口运行
dotnet run --urls "http://localhost:5000"
```

#### 4.2 生产模式运行
```bash
# 发布项目
dotnet publish -c Release -o ./publish

# 运行发布版本
cd publish
./WebAutomation.exe
```

### 5. 验证安装

#### 5.1 Web界面测试
1. 打开浏览器访问: `http://localhost:5096`
2. 应该看到WebAutomation控制台界面

#### 5.2 API文档测试
1. 访问: `http://localhost:5096/swagger`
2. 查看完整的API文档

#### 5.3 功能测试
运行演示脚本：
```powershell
# Windows PowerShell
.\Examples\demo.ps1
```

## 🔧 常见问题解决

### Q1: Chrome浏览器无法启动
**解决方案:**
1. 确保Chrome浏览器已正确安装
2. 检查ChromeDriver版本是否与Chrome版本兼容
3. 尝试以管理员权限运行

### Q2: DeepSeek API调用失败
**解决方案:**
1. 检查API密钥是否正确配置
2. 确认网络连接正常
3. 验证API密钥是否有效且有足够余额

### Q3: 端口被占用
**解决方案:**
```bash
# 查看端口占用
netstat -ano | findstr :5096

# 更换端口运行
dotnet run --urls "http://localhost:8080"
```

### Q4: 微信网页版登录失败
**解决方案:**
1. 确保网络连接正常
2. 手动访问 wx.qq.com 确认可以正常访问
3. 检查是否被微信限制登录

### Q5: 新闻爬虫无法获取内容
**解决方案:**
1. 检查目标网站是否可正常访问
2. 某些网站可能有反爬虫机制
3. 尝试增加等待时间或更换User-Agent

## 🛡️ 安全注意事项

### API密钥安全
- ❌ 不要将API密钥提交到版本控制系统
- ✅ 使用环境变量或安全的配置管理
- ✅ 定期轮换API密钥

### 网络安全
- ✅ 在生产环境中使用HTTPS
- ✅ 配置适当的CORS策略
- ✅ 添加身份验证和授权

### 爬虫合规
- ✅ 遵守目标网站的robots.txt
- ✅ 控制请求频率，避免对服务器造成压力
- ✅ 遵守相关法律法规

## 📈 性能优化建议

### Chrome优化
```json
{
  "Chrome": {
    "DefaultHeadless": true,  // 生产环境建议使用无头模式
    "DisableImages": true,    // 如不需要图片可禁用以提高速度
    "Arguments": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-gpu"
    ]
  }
}
```

### 爬虫优化
- 使用连接池复用HTTP连接
- 合理设置并发数量
- 实现缓存机制避免重复请求

## 🔄 更新升级

### 更新依赖包
```bash
# 检查过时的包
dotnet list package --outdated

# 更新包
dotnet add package <PackageName> --version <NewVersion>
```

### 更新ChromeDriver
ChromeDriver会自动下载匹配的版本，但如果遇到问题：
```bash
# 手动更新ChromeDriver包
dotnet add package Selenium.WebDriver.ChromeDriver --version <LatestVersion>
```

## 📞 获取帮助

如果遇到问题：
1. 查看项目的 [README.md](./README.md) 文件
2. 检查 [Issues](../../issues) 页面
3. 创建新的Issue描述问题
4. 联系项目维护者

---

**祝您使用愉快！** 🎉
