using System.ComponentModel.DataAnnotations;

namespace WebAutomation.Server.Models
{
    /// <summary>
    /// 网页爬虫结果模型
    /// </summary>
    public class ScrapingResult
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        public string Url { get; set; } = string.Empty;
        
        public string Title { get; set; } = string.Empty;
        
        public string Content { get; set; } = string.Empty;
        
        public string Summary { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public string Source { get; set; } = string.Empty;
        
        public List<string> Images { get; set; } = new List<string>();
        
        public List<string> Links { get; set; } = new List<string>();
        
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
        
        public bool IsSuccess { get; set; } = true;
        
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 爬虫请求模型
    /// </summary>
    public class ScrapingRequest
    {
        [Required]
        public string Url { get; set; } = string.Empty;
        
        public bool IncludeImages { get; set; } = true;
        
        public bool IncludeLinks { get; set; } = true;
        
        public bool GenerateSummary { get; set; } = true;
        
        public int WaitTimeSeconds { get; set; } = 3;
        
        public string? CustomSelector { get; set; }
        
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// 微信消息模型
    /// </summary>
    public class WeChatMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string Sender { get; set; } = string.Empty;
        
        public string Content { get; set; } = string.Empty;
        
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        public string MessageType { get; set; } = "text"; // text, image, file, etc.
        
        public string? ImageUrl { get; set; }
        
        public string? FileUrl { get; set; }
        
        public bool IsRead { get; set; } = false;
    }

    /// <summary>
    /// 新闻文章模型
    /// </summary>
    public class NewsArticle
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        public string Title { get; set; } = string.Empty;
        
        public string Content { get; set; } = string.Empty;
        
        public string Summary { get; set; } = string.Empty;
        
        public string Author { get; set; } = string.Empty;
        
        public DateTime PublishedAt { get; set; }
        
        public string Source { get; set; } = string.Empty;
        
        public string Url { get; set; } = string.Empty;
        
        public List<string> Tags { get; set; } = new List<string>();
        
        public string? ImageUrl { get; set; }
        
        public int ReadTime { get; set; } // 预估阅读时间（分钟）
    }

    /// <summary>
    /// DeepSeek API响应模型
    /// </summary>
    public class DeepSeekResponse
    {
        public string Id { get; set; } = string.Empty;
        
        public string Object { get; set; } = string.Empty;
        
        public long Created { get; set; }
        
        public string Model { get; set; } = string.Empty;
        
        public List<Choice> Choices { get; set; } = new List<Choice>();
        
        public Usage Usage { get; set; } = new Usage();
    }

    public class Choice
    {
        public int Index { get; set; }
        
        public Message Message { get; set; } = new Message();
        
        public string FinishReason { get; set; } = string.Empty;
    }

    public class Message
    {
        public string Role { get; set; } = string.Empty;
        
        public string Content { get; set; } = string.Empty;
    }

    public class Usage
    {
        public int PromptTokens { get; set; }
        
        public int CompletionTokens { get; set; }
        
        public int TotalTokens { get; set; }
    }

    /// <summary>
    /// Chrome自动化配置
    /// </summary>
    public class ChromeOptions
    {
        public bool Headless { get; set; } = false;
        
        public bool DisableImages { get; set; } = false;
        
        public bool DisableJavaScript { get; set; } = false;
        
        public string? UserAgent { get; set; }
        
        public string? ProxyServer { get; set; }
        
        public int WindowWidth { get; set; } = 1920;
        
        public int WindowHeight { get; set; } = 1080;
        
        public int PageLoadTimeout { get; set; } = 30;
        
        public List<string> Arguments { get; set; } = new List<string>();
    }
}
