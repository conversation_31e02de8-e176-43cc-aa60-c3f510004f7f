@page "/chrome"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>Chrome浏览器控制</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">🌐 Chrome浏览器控制</MudText>

    <!-- 浏览器状态卡片 -->
    <MudCard Class="mb-4">
        <MudCardContent>
            <MudGrid AlignItems="Center">
                <MudItem xs="12" md="6">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@(_isConnected ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                 Color="@(_isConnected ? Color.Success : Color.Error)" 
                                 Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h6">浏览器状态</MudText>
                            <MudText Typo="Typo.body2" Color="@(_isConnected ? Color.Success : Color.Error)">
                                @(_isConnected ? "已连接" : "未连接")
                            </MudText>
                        </div>
                    </div>
                </MudItem>
                <MudItem xs="12" md="6" Class="text-right">
                    <MudButton Variant="Variant.Filled" 
                               Color="@(_isConnected ? Color.Error : Color.Primary)"
                               StartIcon="@(_isConnected ? Icons.Material.Filled.Stop : Icons.Material.Filled.PlayArrow)"
                               OnClick="@(_isConnected ? DisconnectChrome : InitializeChrome)"
                               Disabled="_isLoading">
                        @(_isConnected ? "断开连接" : "初始化Chrome")
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <MudGrid>
        <!-- 浏览器设置 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">⚙️ 浏览器设置</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12">
                            <MudCheckBox @bind-Value="_chromeOptions.Headless" Label="无头模式" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudCheckBox @bind-Value="_chromeOptions.DisableImages" Label="禁用图片" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudCheckBox @bind-Value="_chromeOptions.DisableJavaScript" Label="禁用JavaScript" />
                        </MudItem>
                        <MudItem xs="6">
                            <MudNumericField @bind-Value="_chromeOptions.WindowWidth" 
                                           Label="窗口宽度" 
                                           Min="800" Max="3840" />
                        </MudItem>
                        <MudItem xs="6">
                            <MudNumericField @bind-Value="_chromeOptions.WindowHeight" 
                                           Label="窗口高度" 
                                           Min="600" Max="2160" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_chromeOptions.UserAgent" 
                                        Label="User Agent" 
                                        Placeholder="留空使用默认" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_chromeOptions.ProxyServer" 
                                        Label="代理服务器" 
                                        Placeholder="例如: 127.0.0.1:8080" />
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 页面导航 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🔗 页面导航</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_navigationUrl" 
                                        Label="目标URL" 
                                        Placeholder="https://example.com"
                                        Adornment="Adornment.End"
                                        AdornmentIcon="Icons.Material.Filled.Language" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Primary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Navigation"
                                     OnClick="NavigateToUrl"
                                     Disabled="@(!_isConnected || _isLoading || string.IsNullOrEmpty(_navigationUrl))">
                                导航到页面
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.ArrowBack"
                                     OnClick="GoBack"
                                     Disabled="@(!_isConnected || _isLoading)">
                                后退
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.ArrowForward"
                                     OnClick="GoForward"
                                     Disabled="@(!_isConnected || _isLoading)">
                                前进
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 元素操作 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🖱️ 元素操作</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_elementSelector" 
                                        Label="CSS选择器" 
                                        Placeholder="例如: #button-id 或 .class-name"
                                        Adornment="Adornment.End"
                                        AdornmentIcon="Icons.Material.Filled.Code" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_inputText" 
                                        Label="输入文本" 
                                        Placeholder="要输入的文本内容" />
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Info" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.TouchApp"
                                     OnClick="ClickElement"
                                     Disabled="@(!_isConnected || _isLoading || string.IsNullOrEmpty(_elementSelector))">
                                点击元素
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Warning" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Keyboard"
                                     OnClick="InputTextToElement"
                                     Disabled="@(!_isConnected || _isLoading || string.IsNullOrEmpty(_elementSelector) || string.IsNullOrEmpty(_inputText))">
                                输入文本
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 页面操作 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📄 页面操作</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Success" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.CameraAlt"
                                     OnClick="TakeScreenshot"
                                     Disabled="@(!_isConnected || _isLoading)">
                                截图
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Tertiary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Refresh"
                                     OnClick="RefreshPage"
                                     Disabled="@(!_isConnected || _isLoading)">
                                刷新页面
                            </MudButton>
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_jsCode" 
                                        Label="JavaScript代码" 
                                        Lines="3"
                                        Placeholder="输入要执行的JavaScript代码" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Dark" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Code"
                                     OnClick="ExecuteJavaScript"
                                     Disabled="@(!_isConnected || _isLoading || string.IsNullOrEmpty(_jsCode))">
                                执行JavaScript
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 截图显示 -->
    @if (!string.IsNullOrEmpty(_screenshotData))
    {
        <MudCard Class="mb-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">📸 页面截图</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton StartIcon="Icons.Material.Filled.Download" 
                             Color="Color.Primary" 
                             OnClick="DownloadScreenshot">
                        下载截图
                    </MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <img src="@_screenshotData" style="max-width: 100%; height: auto; border-radius: 8px;" alt="页面截图" />
            </MudCardContent>
        </MudCard>
    }

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

@code {
    private bool _isConnected = false;
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private string _navigationUrl = "";
    private string _elementSelector = "";
    private string _inputText = "";
    private string _jsCode = "";
    private string _screenshotData = "";

    private ChromeOptions _chromeOptions = new ChromeOptions
    {
        Headless = false,
        DisableImages = false,
        DisableJavaScript = false,
        WindowWidth = 1280,
        WindowHeight = 720,
        PageLoadTimeout = 30
    };

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        _isConnected = StateService.SystemStatus.ChromeConnected;
    }

    private async Task InitializeChrome()
    {
        _isLoading = true;
        _loadingMessage = "正在初始化Chrome浏览器...";

        try
        {
            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/init", _chromeOptions);
            var result = await response.Content.ReadFromJsonAsync<dynamic>();

            if (result?.success == true)
            {
                _isConnected = true;
                await StateService.UpdateChromeStatusAsync(true);
                await NotificationService.ShowSuccessAsync("Chrome浏览器已成功启动");
            }
            else
            {
                await NotificationService.ShowErrorAsync(result?.message?.ToString() ?? "未知错误");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task DisconnectChrome()
    {
        _isConnected = false;
        await StateService.UpdateChromeStatusAsync(false);
        await NotificationService.ShowChromeResultAsync(true, "断开连接", "Chrome浏览器连接已断开");
    }

    private async Task NavigateToUrl()
    {
        if (string.IsNullOrEmpty(_navigationUrl)) return;

        _isLoading = true;
        _loadingMessage = $"正在导航到 {_navigationUrl}...";

        try
        {
            var request = new { Url = _navigationUrl };
            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/navigate", request);
            var result = await response.Content.ReadFromJsonAsync<dynamic>();

            if (result?.success == true)
            {
                await NotificationService.ShowSuccessAsync($"成功导航到 {_navigationUrl}");
            }
            else
            {
                await NotificationService.ShowErrorAsync(result?.message?.ToString() ?? "导航失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ClickElement()
    {
        if (string.IsNullOrEmpty(_elementSelector)) return;

        _isLoading = true;
        _loadingMessage = "正在点击元素...";

        try
        {
            var request = new { Selector = _elementSelector, TimeoutSeconds = 10 };
            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/click", request);
            var result = await response.Content.ReadFromJsonAsync<dynamic>();

            if (result?.success == true)
            {
                await NotificationService.ShowSuccessAsync($"成功点击元素: {_elementSelector}");
            }
            else
            {
                await NotificationService.ShowErrorAsync(result?.message?.ToString() ?? "点击失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task InputTextToElement()
    {
        if (string.IsNullOrEmpty(_elementSelector) || string.IsNullOrEmpty(_inputText)) return;

        _isLoading = true;
        _loadingMessage = "正在输入文本...";

        try
        {
            var request = new { Selector = _elementSelector, Text = _inputText, TimeoutSeconds = 10 };
            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/input", request);
            var result = await response.Content.ReadFromJsonAsync<dynamic>();

            if (result?.success == true)
            {
                await NotificationService.ShowSuccessAsync($"成功输入文本到: {_elementSelector}");
            }
            else
            {
                await NotificationService.ShowErrorAsync(result?.message?.ToString() ?? "输入失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task TakeScreenshot()
    {
        _isLoading = true;
        _loadingMessage = "正在截图...";

        try
        {
            var response = await HttpClient.GetAsync("/api/automation/chrome/screenshot");
            if (response.IsSuccessStatusCode)
            {
                var screenshotBytes = await response.Content.ReadAsByteArrayAsync();
                _screenshotData = $"data:image/png;base64,{Convert.ToBase64String(screenshotBytes)}";
                await NotificationService.ShowSuccessAsync("截图成功");
            }
            else
            {
                await NotificationService.ShowErrorAsync("截图失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task RefreshPage()
    {
        _isLoading = true;
        _loadingMessage = "正在刷新页面...";

        try
        {
            // 刷新当前页面
            var request = new { Url = "javascript:location.reload();" };
            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/navigate", request);
            await NotificationService.ShowSuccessAsync("页面已刷新");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ExecuteJavaScript()
    {
        if (string.IsNullOrEmpty(_jsCode)) return;

        _isLoading = true;
        _loadingMessage = "正在执行JavaScript...";

        try
        {
            // 这里可以添加执行JavaScript的API调用
            await Task.Delay(1000); // 模拟操作
            await NotificationService.ShowSuccessAsync("JavaScript代码执行成功");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task GoBack()
    {
        try
        {
            var request = new { Url = "javascript:history.back();" };
            await HttpClient.PostAsJsonAsync("/api/automation/chrome/navigate", request);
            await NotificationService.ShowSuccessAsync("页面后退成功");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
    }

    private async Task GoForward()
    {
        try
        {
            var request = new { Url = "javascript:history.forward();" };
            await HttpClient.PostAsJsonAsync("/api/automation/chrome/navigate", request);
            await NotificationService.ShowSuccessAsync("页面前进成功");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync(ex.Message);
        }
    }

    private async Task DownloadScreenshot()
    {
        if (!string.IsNullOrEmpty(_screenshotData))
        {
            await NotificationService.ShowInfoAsync("开始下载截图文件");
            // 这里可以实现下载截图的功能
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
