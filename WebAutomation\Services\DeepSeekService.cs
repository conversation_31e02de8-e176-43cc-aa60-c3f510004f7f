using Newtonsoft.Json;
using WebAutomation.Models;
using System.Text;

namespace WebAutomation.Services
{
    /// <summary>
    /// DeepSeek API服务
    /// </summary>
    public class DeepSeekService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<DeepSeekService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _apiKey;
        private readonly string _baseUrl;

        public DeepSeekService(HttpClient httpClient, ILogger<DeepSeekService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _apiKey = _configuration["DeepSeek:ApiKey"] ?? throw new ArgumentException("DeepSeek API Key not configured");
            _baseUrl = _configuration["DeepSeek:BaseUrl"] ?? "https://api.deepseek.com/v1";
            
            // 设置HTTP客户端
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "WebAutomation/1.0");
        }

        /// <summary>
        /// 生成内容摘要
        /// </summary>
        public async Task<string> GenerateSummaryAsync(string content, string? customPrompt = null)
        {
            try
            {
                var prompt = customPrompt ?? $@"请对以下内容进行总结，要求：
1. 提取关键信息和要点
2. 保持客观中性的语调
3. 控制在200字以内
4. 使用中文回复

内容：
{content}";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法生成摘要";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成摘要失败");
                return "摘要生成失败";
            }
        }

        /// <summary>
        /// 分析新闻内容
        /// </summary>
        public async Task<string> AnalyzeNewsAsync(string title, string content)
        {
            try
            {
                var prompt = $@"请分析以下新闻内容，提供：
1. 新闻要点总结
2. 关键信息提取
3. 影响分析
4. 相关背景
5. 趋势预测（如适用）

标题：{title}

内容：{content}

请用中文回复，结构清晰，重点突出。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法分析新闻内容";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析新闻失败");
                return "新闻分析失败";
            }
        }

        /// <summary>
        /// 分析微信消息
        /// </summary>
        public async Task<string> AnalyzeWeChatMessagesAsync(List<WeChatMessage> messages)
        {
            try
            {
                var messageText = string.Join("\n", messages.Select(m => $"[{m.Timestamp:HH:mm}] {m.Sender}: {m.Content}"));
                
                var prompt = $@"请分析以下微信聊天记录，提供：
1. 对话主题总结
2. 关键信息提取
3. 情感倾向分析
4. 重要事项提醒
5. 后续建议

聊天记录：
{messageText}

请用中文回复，分析要客观准确。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法分析微信消息";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析微信消息失败");
                return "微信消息分析失败";
            }
        }

        /// <summary>
        /// 生成技术文章摘要
        /// </summary>
        public async Task<string> GenerateTechSummaryAsync(string content)
        {
            try
            {
                var prompt = $@"请对以下技术文章进行专业分析，包括：
1. 技术要点总结
2. 核心概念解释
3. 实用价值评估
4. 技术趋势分析
5. 学习建议

文章内容：
{content}

请用中文回复，突出技术重点。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法生成技术摘要";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成技术摘要失败");
                return "技术摘要生成失败";
            }
        }

        /// <summary>
        /// 自定义分析
        /// </summary>
        public async Task<string> CustomAnalysisAsync(string content, string analysisType)
        {
            try
            {
                var prompt = $@"请对以下内容进行{analysisType}分析：

内容：
{content}

请提供详细、准确的分析结果，使用中文回复。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法完成自定义分析";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自定义分析失败");
                return "自定义分析失败";
            }
        }

        /// <summary>
        /// 调用DeepSeek API
        /// </summary>
        private async Task<DeepSeekResponse?> CallDeepSeekApiAsync(string prompt, string model = "deepseek-chat")
        {
            try
            {
                var requestBody = new
                {
                    model = model,
                    messages = new[]
                    {
                        new
                        {
                            role = "user",
                            content = prompt
                        }
                    },
                    max_tokens = 2000,
                    temperature = 0.7,
                    top_p = 0.9,
                    frequency_penalty = 0,
                    presence_penalty = 0
                };

                var json = JsonConvert.SerializeObject(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/chat/completions", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var deepSeekResponse = JsonConvert.DeserializeObject<DeepSeekResponse>(responseContent);
                    
                    _logger.LogInformation("DeepSeek API调用成功");
                    return deepSeekResponse;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"DeepSeek API调用失败: {response.StatusCode}, {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用DeepSeek API时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 检查API连接状态
        /// </summary>
        public async Task<bool> CheckApiStatusAsync()
        {
            try
            {
                var testPrompt = "Hello, this is a test message.";
                var response = await CallDeepSeekApiAsync(testPrompt);
                return response != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查DeepSeek API状态失败");
                return false;
            }
        }
    }
}
