using Newtonsoft.Json;
using System.Text;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    /// <summary>
    /// DeepSeek AI服务
    /// </summary>
    public class DeepSeekService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DeepSeekService> _logger;
        private readonly string _apiKey;
        private readonly string _baseUrl;

        public DeepSeekService(HttpClient httpClient, IConfiguration configuration, ILogger<DeepSeekService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            
            _apiKey = _configuration["DeepSeek:ApiKey"] ?? "";
            _baseUrl = _configuration["DeepSeek:BaseUrl"] ?? "https://api.deepseek.com/v1";
            
            // 设置HTTP客户端
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "WebAutomation/1.0");
        }

        /// <summary>
        /// 检查API状态
        /// </summary>
        public async Task<bool> CheckApiStatusAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_apiKey))
                {
                    _logger.LogWarning("DeepSeek API密钥未配置");
                    return false;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/models");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查DeepSeek API状态失败");
                return false;
            }
        }

        /// <summary>
        /// 调用DeepSeek API
        /// </summary>
        private async Task<DeepSeekResponse?> CallDeepSeekApiAsync(string prompt, string model = "deepseek-chat")
        {
            try
            {
                var request = new DeepSeekRequest
                {
                    Model = model,
                    Messages = new List<DeepSeekMessage>
                    {
                        new DeepSeekMessage
                        {
                            Role = "user",
                            Content = prompt
                        }
                    },
                    MaxTokens = 2000,
                    Temperature = 0.7
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/chat/completions", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<DeepSeekResponse>(responseJson);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("DeepSeek API调用失败: {StatusCode}, {Content}", response.StatusCode, errorContent);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用DeepSeek API时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 生成内容摘要
        /// </summary>
        public async Task<string> GenerateSummaryAsync(string content, string? customPrompt = null)
        {
            try
            {
                var prompt = customPrompt ?? $@"请对以下内容进行摘要，要求：
1. 提取核心要点
2. 保持逻辑清晰
3. 控制在200字以内
4. 使用中文回复

内容：
{content}";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法生成摘要";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成摘要失败");
                return "摘要生成失败";
            }
        }

        /// <summary>
        /// 分析新闻内容
        /// </summary>
        public async Task<string> AnalyzeNewsAsync(NewsArticle article)
        {
            try
            {
                var prompt = $@"请分析以下新闻文章，提供：
1. 新闻要点总结
2. 影响分析
3. 相关技术解读
4. 市场意义
5. 发展趋势预测

新闻标题：{article.Title}
新闻来源：{article.Source}
新闻内容：{article.Content}

请用中文回复，结构清晰，重点突出。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法分析新闻内容";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析新闻失败");
                return "新闻分析失败";
            }
        }

        /// <summary>
        /// 分析微信消息
        /// </summary>
        public async Task<string> AnalyzeWeChatMessagesAsync(List<WeChatMessage> messages)
        {
            try
            {
                var messageText = string.Join("\n", messages.Select(m => $"[{m.Timestamp:HH:mm}] {m.Sender}: {m.Content}"));
                
                var prompt = $@"请分析以下微信聊天记录，提供：
1. 对话主题总结
2. 关键信息提取
3. 情感倾向分析
4. 重要事项提醒
5. 后续建议

聊天记录：
{messageText}

请用中文回复，分析要客观准确。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法分析微信消息";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析微信消息失败");
                return "微信消息分析失败";
            }
        }

        /// <summary>
        /// 生成技术文章摘要
        /// </summary>
        public async Task<string> GenerateTechSummaryAsync(string content)
        {
            try
            {
                var prompt = $@"请对以下技术文章进行专业分析，包括：
1. 技术要点总结
2. 核心概念解释
3. 实用价值评估
4. 技术趋势分析
5. 学习建议

文章内容：
{content}

请用中文回复，专业准确，适合技术人员阅读。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法生成技术摘要";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成技术摘要失败");
                return "技术摘要生成失败";
            }
        }

        /// <summary>
        /// 智能问答
        /// </summary>
        public async Task<string> ChatAsync(string question, string? context = null)
        {
            try
            {
                var prompt = string.IsNullOrEmpty(context) 
                    ? question 
                    : $"基于以下上下文回答问题：\n\n上下文：{context}\n\n问题：{question}";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "无法回答问题";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "智能问答失败");
                return "问答失败";
            }
        }

        /// <summary>
        /// 内容分类
        /// </summary>
        public async Task<List<string>> ClassifyContentAsync(string content)
        {
            try
            {
                var prompt = $@"请对以下内容进行分类，从以下类别中选择最合适的（可多选）：
- 科技新闻
- 产品发布
- 行业分析
- 技术教程
- 市场动态
- 投资融资
- 人事变动
- 政策法规
- 其他

内容：
{content}

请只返回分类标签，用逗号分隔。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    var categories = response.Choices[0].Message.Content
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(c => c.Trim())
                        .ToList();
                    
                    return categories;
                }

                return new List<string> { "其他" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "内容分类失败");
                return new List<string> { "其他" };
            }
        }

        /// <summary>
        /// 情感分析
        /// </summary>
        public async Task<string> AnalyzeSentimentAsync(string text)
        {
            try
            {
                var prompt = $@"请分析以下文本的情感倾向，从以下选项中选择：
- 积极
- 消极
- 中性

并简要说明理由。

文本：
{text}";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "中性";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "情感分析失败");
                return "中性";
            }
        }

        /// <summary>
        /// 关键词提取
        /// </summary>
        public async Task<List<string>> ExtractKeywordsAsync(string content, int maxKeywords = 10)
        {
            try
            {
                var prompt = $@"请从以下内容中提取最重要的{maxKeywords}个关键词，按重要性排序：

内容：
{content}

请只返回关键词，用逗号分隔。";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    var keywords = response.Choices[0].Message.Content
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(k => k.Trim())
                        .Take(maxKeywords)
                        .ToList();
                    
                    return keywords;
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关键词提取失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 翻译文本
        /// </summary>
        public async Task<string> TranslateAsync(string text, string targetLanguage = "中文")
        {
            try
            {
                var prompt = $@"请将以下文本翻译成{targetLanguage}：

{text}";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "翻译失败";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "翻译失败");
                return "翻译失败";
            }
        }

        /// <summary>
        /// 生成代码
        /// </summary>
        public async Task<string> GenerateCodeAsync(string description, string language = "C#")
        {
            try
            {
                var prompt = $@"请根据以下描述生成{language}代码：

{description}

要求：
1. 代码规范，注释清晰
2. 包含必要的错误处理
3. 遵循最佳实践";

                var response = await CallDeepSeekApiAsync(prompt);
                
                if (response?.Choices?.Count > 0)
                {
                    return response.Choices[0].Message.Content;
                }

                return "代码生成失败";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "代码生成失败");
                return "代码生成失败";
            }
        }
    }

    /// <summary>
    /// DeepSeek API请求模型
    /// </summary>
    public class DeepSeekRequest
    {
        [JsonProperty("model")]
        public string Model { get; set; } = "";

        [JsonProperty("messages")]
        public List<DeepSeekMessage> Messages { get; set; } = new();

        [JsonProperty("max_tokens")]
        public int MaxTokens { get; set; } = 2000;

        [JsonProperty("temperature")]
        public double Temperature { get; set; } = 0.7;

        [JsonProperty("stream")]
        public bool Stream { get; set; } = false;
    }

    /// <summary>
    /// DeepSeek消息模型
    /// </summary>
    public class DeepSeekMessage
    {
        [JsonProperty("role")]
        public string Role { get; set; } = "";

        [JsonProperty("content")]
        public string Content { get; set; } = "";
    }

    /// <summary>
    /// DeepSeek API响应模型
    /// </summary>
    public class DeepSeekResponse
    {
        [JsonProperty("id")]
        public string Id { get; set; } = "";

        [JsonProperty("object")]
        public string Object { get; set; } = "";

        [JsonProperty("created")]
        public long Created { get; set; }

        [JsonProperty("model")]
        public string Model { get; set; } = "";

        [JsonProperty("choices")]
        public List<DeepSeekChoice> Choices { get; set; } = new();

        [JsonProperty("usage")]
        public DeepSeekUsage Usage { get; set; } = new();
    }

    /// <summary>
    /// DeepSeek选择模型
    /// </summary>
    public class DeepSeekChoice
    {
        [JsonProperty("index")]
        public int Index { get; set; }

        [JsonProperty("message")]
        public DeepSeekMessage Message { get; set; } = new();

        [JsonProperty("finish_reason")]
        public string FinishReason { get; set; } = "";
    }

    /// <summary>
    /// DeepSeek使用情况模型
    /// </summary>
    public class DeepSeekUsage
    {
        [JsonProperty("prompt_tokens")]
        public int PromptTokens { get; set; }

        [JsonProperty("completion_tokens")]
        public int CompletionTokens { get; set; }

        [JsonProperty("total_tokens")]
        public int TotalTokens { get; set; }
    }
}
