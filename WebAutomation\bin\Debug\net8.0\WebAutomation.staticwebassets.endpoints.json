{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17867"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"m3vZVmRMScRupxblw5tgVLGxi9BiDYginC+xMbKxYrE=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:23:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m3vZVmRMScRupxblw5tgVLGxi9BiDYginC+xMbKxYrE="}]}, {"Route": "index.nl1a19vgud.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17867"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"m3vZVmRMScRupxblw5tgVLGxi9BiDYginC+xMbKxYrE=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:23:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nl1a19vgud"}, {"Name": "integrity", "Value": "sha256-m3vZVmRMScRupxblw5tgVLGxi9BiDYginC+xMbKxYrE="}, {"Name": "label", "Value": "index.html"}]}]}