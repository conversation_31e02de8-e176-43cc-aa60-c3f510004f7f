{"Version": 1, "Hash": "jxZex/vXKNgqvQeSYnheJA506nWbhQNpdCvSXmrwJjw=", "Source": "WebAutomation", "BasePath": "_content/WebAutomation", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "WebAutomation\\wwwroot", "Source": "WebAutomation", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\", "BasePath": "_content/WebAutomation", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1xiwpnv0y9", "Integrity": "O3PacWydT7j+X5HkANozalNed2xkCCEFu1PFI3zUnJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rftke82za3", "Integrity": "+V9u6OnLOySMfSLVV3IoCOm/yPD3Gh4yn+ZPHPNn5CE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\6.11.2\\staticwebassets\\MudBlazor.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\css\\app.css", "SourceId": "WebAutomation", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\", "BasePath": "_content/WebAutomation", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "grns6og31q", "Integrity": "tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\index.html", "SourceId": "WebAutomation", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\", "BasePath": "_content/WebAutomation", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h01dewflzc", "Integrity": "YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\js\\app.js", "SourceId": "WebAutomation", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\", "BasePath": "_content/WebAutomation", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6vu70btkbq", "Integrity": "/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js"}], "Endpoints": [{"Route": "css/app.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6766"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 05:00:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI="}]}, {"Route": "css/app.grns6og31q.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6766"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 05:00:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "grns6og31q"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI="}]}, {"Route": "index.h01dewflzc.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:53:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h01dewflzc"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:53:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM="}]}, {"Route": "js/app.6vu70btkbq.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:54:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6vu70btkbq"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\csharpChrome\\WebAutomation\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:54:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0="}]}]}