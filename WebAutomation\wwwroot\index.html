<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebAutomation - 智能自动化平台</title>
    <base href="/" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="css/app.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="WebAutomation.styles.css" rel="stylesheet" />

</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WebAutomation</h1>
            <p>Chrome自动化控制 & 智能网页爬虫 & AI分析平台</p>
        </div>

        <div class="content">
            <!-- Chrome控制区域 -->
            <div class="section">
                <h2>🌐 Chrome浏览器控制</h2>
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label>浏览器设置</label>
                            <label><input type="checkbox" id="headless"> 无头模式</label>
                            <label><input type="checkbox" id="disableImages"> 禁用图片</label>
                        </div>
                        <button onclick="initChrome()">初始化Chrome</button>
                        <button onclick="takeScreenshot()">截图</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="urlInput">导航URL:</label>
                            <input type="url" id="urlInput" placeholder="https://example.com">
                        </div>
                        <button onclick="navigateToUrl()">导航</button>
                    </div>
                </div>
                <div id="chromeResult" class="result" style="display:none;"></div>
            </div>

            <!-- 网页爬虫区域 -->
            <div class="section">
                <h2>🕷️ 网页爬虫</h2>
                <div class="form-group">
                    <label for="scrapeUrl">爬取URL:</label>
                    <input type="url" id="scrapeUrl" placeholder="https://example.com">
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="generateSummary" checked> 生成AI摘要</label>
                </div>
                <button onclick="scrapeWebPage()">开始爬取</button>
                <div id="scrapeResult" class="result" style="display:none;"></div>
            </div>

            <!-- 微信控制区域 -->
            <div class="section">
                <h2>💬 微信网页版</h2>
                <div class="grid">
                    <div>
                        <button onclick="wechatLogin()">登录微信</button>
                        <button onclick="getWechatChats()">获取聊天列表</button>
                        <button onclick="getWechatMessages()">获取消息</button>
                    </div>
                    <div>
                        <button onclick="analyzeWechatMessages()">分析消息</button>
                        <span id="wechatStatus" class="status offline">离线</span>
                    </div>
                </div>
                <div id="wechatResult" class="result" style="display:none;"></div>
            </div>

            <!-- 新闻爬虫区域 -->
            <div class="section">
                <h2>📰 科技新闻爬虫</h2>
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label for="newsSource">新闻源:</label>
                            <select id="newsSource">
                                <option value="36kr">36氪</option>
                                <option value="ithome">IT之家</option>
                                <option value="cnbeta">cnBeta</option>
                                <option value="techcrunch">TechCrunch</option>
                            </select>
                        </div>
                        <button onclick="scrapeNews()">爬取新闻</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="maxArticles">文章数量:</label>
                            <input type="number" id="maxArticles" value="5" min="1" max="20">
                        </div>
                        <button onclick="scrapeMultipleNews()">批量爬取</button>
                    </div>
                </div>
                <div id="newsResult" class="result" style="display:none;"></div>
            </div>

            <!-- AI分析区域 -->
            <div class="section">
                <h2>🤖 AI智能分析</h2>
                <div class="form-group">
                    <label for="aiContent">分析内容:</label>
                    <textarea id="aiContent" rows="4" placeholder="输入要分析的内容..."></textarea>
                </div>
                <button onclick="generateSummary()">生成摘要</button>
                <button onclick="checkAIStatus()">检查AI状态</button>
                <span id="aiStatus" class="status offline">未知</span>
                <div id="aiResult" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/automation';

        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        // API调用封装
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '请求失败');
                }
                
                return result;
            } catch (error) {
                throw new Error(`API调用失败: ${error.message}`);
            }
        }

        // Chrome控制函数
        async function initChrome() {
            try {
                const options = {
                    headless: document.getElementById('headless').checked,
                    disableImages: document.getElementById('disableImages').checked
                };
                
                const result = await apiCall('/chrome/init', 'POST', options);
                showResult('chromeResult', result);
            } catch (error) {
                showResult('chromeResult', error.message, true);
            }
        }

        async function navigateToUrl() {
            try {
                const url = document.getElementById('urlInput').value;
                if (!url) {
                    throw new Error('请输入URL');
                }
                
                const result = await apiCall('/chrome/navigate', 'POST', { url });
                showResult('chromeResult', result);
            } catch (error) {
                showResult('chromeResult', error.message, true);
            }
        }

        async function takeScreenshot() {
            try {
                const response = await fetch(`${API_BASE}/chrome/screenshot`);
                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    const img = document.createElement('img');
                    img.src = url;
                    img.style.maxWidth = '100%';
                    
                    const resultDiv = document.getElementById('chromeResult');
                    resultDiv.innerHTML = '';
                    resultDiv.appendChild(img);
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'result success';
                } else {
                    throw new Error('截图失败');
                }
            } catch (error) {
                showResult('chromeResult', error.message, true);
            }
        }

        // 网页爬虫函数
        async function scrapeWebPage() {
            try {
                const url = document.getElementById('scrapeUrl').value;
                if (!url) {
                    throw new Error('请输入要爬取的URL');
                }
                
                const request = {
                    url,
                    generateSummary: document.getElementById('generateSummary').checked,
                    includeImages: true,
                    includeLinks: true,
                    waitTimeSeconds: 3
                };
                
                const result = await apiCall('/scraping/webpage', 'POST', request);
                showResult('scrapeResult', result);
            } catch (error) {
                showResult('scrapeResult', error.message, true);
            }
        }

        // 微信函数
        async function wechatLogin() {
            try {
                const result = await apiCall('/wechat/login', 'POST');
                showResult('wechatResult', result);
                if (result.success) {
                    document.getElementById('wechatStatus').className = 'status online';
                    document.getElementById('wechatStatus').textContent = '在线';
                }
            } catch (error) {
                showResult('wechatResult', error.message, true);
            }
        }

        async function getWechatChats() {
            try {
                const result = await apiCall('/wechat/chats');
                showResult('wechatResult', result);
            } catch (error) {
                showResult('wechatResult', error.message, true);
            }
        }

        async function getWechatMessages() {
            try {
                const result = await apiCall('/wechat/messages');
                showResult('wechatResult', result);
            } catch (error) {
                showResult('wechatResult', error.message, true);
            }
        }

        async function analyzeWechatMessages() {
            try {
                // 这里应该先获取消息，然后分析
                const messages = await apiCall('/wechat/messages');
                if (messages.messages && messages.messages.length > 0) {
                    const analysis = await apiCall('/wechat/analyze', 'POST', messages.messages);
                    showResult('wechatResult', analysis);
                } else {
                    throw new Error('没有消息可分析');
                }
            } catch (error) {
                showResult('wechatResult', error.message, true);
            }
        }

        // 新闻爬虫函数
        async function scrapeNews() {
            try {
                const source = document.getElementById('newsSource').value;
                const maxArticles = document.getElementById('maxArticles').value;
                
                const result = await apiCall(`/news/${source}?maxArticles=${maxArticles}`);
                showResult('newsResult', result);
            } catch (error) {
                showResult('newsResult', error.message, true);
            }
        }

        async function scrapeMultipleNews() {
            try {
                const sources = ['36kr', 'ithome', 'cnbeta'];
                const articlesPerSource = parseInt(document.getElementById('maxArticles').value);
                
                const request = {
                    sources,
                    articlesPerSource
                };
                
                const result = await apiCall('/news/batch', 'POST', request);
                showResult('newsResult', result);
            } catch (error) {
                showResult('newsResult', error.message, true);
            }
        }

        // AI分析函数
        async function generateSummary() {
            try {
                const content = document.getElementById('aiContent').value;
                if (!content) {
                    throw new Error('请输入要分析的内容');
                }
                
                const result = await apiCall('/ai/summary', 'POST', { content });
                showResult('aiResult', result);
            } catch (error) {
                showResult('aiResult', error.message, true);
            }
        }

        async function checkAIStatus() {
            try {
                const result = await apiCall('/ai/status');
                showResult('aiResult', result);
                
                const statusElement = document.getElementById('aiStatus');
                if (result.isOnline) {
                    statusElement.className = 'status online';
                    statusElement.textContent = '在线';
                } else {
                    statusElement.className = 'status offline';
                    statusElement.textContent = '离线';
                }
            } catch (error) {
                showResult('aiResult', error.message, true);
                document.getElementById('aiStatus').className = 'status offline';
                document.getElementById('aiStatus').textContent = '错误';
            }
        }

        // 页面加载时检查状态
        window.addEventListener('load', () => {
            checkAIStatus();
        });
    </script>
</body>
</html>
