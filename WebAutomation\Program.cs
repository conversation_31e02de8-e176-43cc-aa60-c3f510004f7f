using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using WebAutomation;
using WebAutomation.Services;
using Blazored.LocalStorage;
using Blazored.Modal;
using Blazored.Toast;
using MudBlazor.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// 配置HTTP客户端
builder.Services.AddScoped(sp => new HttpClient
{
    BaseAddress = new Uri("https://localhost:7001") // API服务器地址
});

// 添加Blazor服务
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddBlazoredModal();
builder.Services.AddBlazoredToast();
builder.Services.AddMudServices();

// 添加自定义服务
builder.Services.AddScoped<ApiService>();
builder.Services.AddScoped<StateService>();
builder.Services.AddScoped<NotificationService>();

await builder.Build().RunAsync();
