using WebAutomation.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 注册HTTP客户端
builder.Services.AddHttpClient();

// 注册自定义服务
builder.Services.AddSingleton<ChromeService>();
builder.Services.AddScoped<WebScrapingService>();
builder.Services.AddScoped<WeChatService>();
builder.Services.AddScoped<NewsScrapingService>();
builder.Services.AddScoped<DeepSeekService>();

// 添加日志
builder.Services.AddLogging();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// 启用静态文件服务
app.UseStaticFiles();

app.UseRouting();
app.MapControllers();

// 添加默认路由
app.MapGet("/", () => "WebAutomation API is running! Visit /swagger for API documentation.");

// 健康检查端点
app.MapGet("/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });

app.Run();
