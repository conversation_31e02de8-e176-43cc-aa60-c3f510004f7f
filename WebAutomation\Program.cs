using WebAutomation.Services;
using WebAutomation.Data;
using MudBlazor.Services;
using Serilog;

// 配置Serilog日志
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/webautomation-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// 添加Serilog
builder.Host.UseSerilog();

// 添加Blazor Server服务
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// 添加控制器和API
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加MudBlazor
builder.Services.AddMudServices();

// 添加Blazored服务
// builder.Services.AddBlazoredLocalStorage();
// builder.Services.AddBlazoredToast();

// 添加HTTP客户端
builder.Services.AddHttpClient();
builder.Services.AddScoped(sp =>
{
    var httpClient = sp.GetRequiredService<HttpClient>();
    httpClient.BaseAddress = new Uri("http://localhost:5096");
    return httpClient;
});

// 添加数据服务
builder.Services.AddSingleton<DatabaseService>();

// 添加业务服务
builder.Services.AddSingleton<ChromeService>();
builder.Services.AddScoped<WebScrapingService>();
builder.Services.AddScoped<WeChatService>();
builder.Services.AddScoped<NewsScrapingService>();
builder.Services.AddScoped<DeepSeekService>();
builder.Services.AddScoped<StateService>();
builder.Services.AddScoped<NotificationService>();
// builder.Services.AddScoped<TaskSchedulerService>();

// 添加Quartz任务调度
// builder.Services.AddQuartz(q =>
// {
//     q.UseMicrosoftDependencyInjection();
// });
// builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

// 添加SignalR
builder.Services.AddSignalR();

// 添加CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseCors("AllowAll");

app.UseRouting();

app.MapRazorPages();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapControllers();

// 健康检查端点
app.MapGet("/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });

// 初始化数据库
using (var scope = app.Services.CreateScope())
{
    var dbService = scope.ServiceProvider.GetRequiredService<DatabaseService>();
    await dbService.InitializeAsync();
}

Log.Information("WebAutomation应用程序启动完成");

app.Run();
