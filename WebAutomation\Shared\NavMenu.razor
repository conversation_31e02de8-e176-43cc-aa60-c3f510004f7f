<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">WebAutomation</a>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <MudNavMenu>
            <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="Icons.Material.Filled.Home">
                🏠 首页
            </MudNavLink>
            
            <MudNavGroup Text="🌐 Chrome控制" Icon="Icons.Material.Filled.Web" Expanded="true">
                <MudNavLink Href="/chrome" Icon="Icons.Material.Filled.Computer">
                    浏览器控制
                </MudNavLink>
                <MudNavLink Href="/chrome/automation" Icon="Icons.Material.Filled.PlayArrow">
                    自动化脚本
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="🕷️ 网页爬虫" Icon="Icons.Material.Filled.Search" Expanded="true">
                <MudNavLink Href="/scraping" Icon="Icons.Material.Filled.Download">
                    内容爬取
                </MudNavLink>
                <MudNavLink Href="/scraping/batch" Icon="Icons.Material.Filled.Batch">
                    批量爬取
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="📰 新闻爬虫" Icon="Icons.Material.Filled.Article" Expanded="true">
                <MudNavLink Href="/news" Icon="Icons.Material.Filled.NewReleases">
                    新闻监控
                </MudNavLink>
                <MudNavLink Href="/news/analysis" Icon="Icons.Material.Filled.Analytics">
                    新闻分析
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="💬 微信管理" Icon="Icons.Material.Filled.Chat" Expanded="true">
                <MudNavLink Href="/wechat" Icon="Icons.Material.Filled.Login">
                    微信控制
                </MudNavLink>
                <MudNavLink Href="/wechat/messages" Icon="Icons.Material.Filled.Message">
                    消息管理
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="🤖 AI分析" Icon="Icons.Material.Filled.Psychology" Expanded="true">
                <MudNavLink Href="/ai" Icon="Icons.Material.Filled.AutoAwesome">
                    智能分析
                </MudNavLink>
                <MudNavLink Href="/ai/summary" Icon="Icons.Material.Filled.Summarize">
                    内容摘要
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="⚙️ 系统管理" Icon="Icons.Material.Filled.Settings" Expanded="false">
                <MudNavLink Href="/settings" Icon="Icons.Material.Filled.Tune">
                    系统设置
                </MudNavLink>
                <MudNavLink Href="/logs" Icon="Icons.Material.Filled.History">
                    操作日志
                </MudNavLink>
                <MudNavLink Href="/about" Icon="Icons.Material.Filled.Info">
                    关于系统
                </MudNavLink>
            </MudNavGroup>
        </MudNavMenu>
    </nav>
</div>

<style>
    .nav-scrollable {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }

    .top-row {
        height: 3.5rem;
        background-color: rgba(0,0,0,0.4);
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-toggler {
        appearance: none;
        cursor: pointer;
        width: 3.5rem;
        height: 2.5rem;
        color: white;
        position: absolute;
        top: 0.5rem;
        right: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler:checked {
        background-color: rgba(255, 255, 255, 0.5);
    }

    @@media (max-width: 640.98px) {
        .top-row {
            display: none;
        }
    }
</style>
