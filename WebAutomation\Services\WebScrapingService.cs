using HtmlAgilityPack;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    /// <summary>
    /// 网页爬虫服务
    /// </summary>
    public class WebScrapingService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebScrapingService> _logger;

        public WebScrapingService(HttpClient httpClient, ILogger<WebScrapingService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            
            // 设置User-Agent
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        }

        /// <summary>
        /// 爬取网页内容
        /// </summary>
        public async Task<ScrapingResult> ScrapeWebPageAsync(ScrapingRequest request)
        {
            var result = new ScrapingResult
            {
                Url = request.Url,
                CreatedAt = DateTime.UtcNow
            };

            try
            {
                // 确保URL有协议前缀
                var url = request.Url;
                if (!url.StartsWith("http://") && !url.StartsWith("https://"))
                {
                    url = "https://" + url;
                }

                _logger.LogInformation("开始爬取网页: {Url}", url);

                // 获取网页内容
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                
                var html = await response.Content.ReadAsStringAsync();
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // 提取标题
                result.Title = ExtractTitle(doc);

                // 提取内容
                if (!string.IsNullOrEmpty(request.ContentSelector))
                {
                    result.Content = ExtractContentBySelector(doc, request.ContentSelector);
                }
                else
                {
                    result.Content = ExtractMainContent(doc);
                }

                // 提取链接
                if (request.ExtractLinks)
                {
                    result.Links = ExtractLinks(doc, url);
                }

                // 提取图片
                if (request.ExtractImages)
                {
                    result.Images = ExtractImages(doc, url);
                }

                // 提取元数据
                var metaData = ExtractMetaData(doc);
                foreach (var kvp in metaData)
                {
                    result.Metadata[kvp.Key] = kvp.Value;
                }

                result.IsSuccess = true;
                result.WordCount = CountWords(result.Content);

                _logger.LogInformation("网页爬取成功: {Url}, 字数: {WordCount}", request.Url, result.WordCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取网页失败: {Url}", request.Url);
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 提取页面标题
        /// </summary>
        private string ExtractTitle(HtmlDocument doc)
        {
            var titleNode = doc.DocumentNode.SelectSingleNode("//title");
            return titleNode?.InnerText?.Trim() ?? "";
        }

        /// <summary>
        /// 根据选择器提取内容
        /// </summary>
        private string ExtractContentBySelector(HtmlDocument doc, string selector)
        {
            try
            {
                var nodes = doc.DocumentNode.SelectNodes(selector);
                if (nodes != null)
                {
                    return string.Join("\n", nodes.Select(n => CleanText(n.InnerText)));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "使用选择器提取内容失败: {Selector}", selector);
            }

            return "";
        }

        /// <summary>
        /// 提取主要内容
        /// </summary>
        private string ExtractMainContent(HtmlDocument doc)
        {
            // 尝试常见的内容选择器
            var contentSelectors = new[]
            {
                "article",
                ".content",
                ".article-content",
                ".post-content",
                ".entry-content",
                "main",
                "#content",
                ".main-content"
            };

            foreach (var selector in contentSelectors)
            {
                var content = ExtractContentBySelector(doc, selector);
                if (!string.IsNullOrEmpty(content) && content.Length > 100)
                {
                    return content;
                }
            }

            // 如果没有找到特定内容区域，提取body中的文本
            var bodyNode = doc.DocumentNode.SelectSingleNode("//body");
            if (bodyNode != null)
            {
                // 移除脚本和样式标签
                var scriptsAndStyles = bodyNode.SelectNodes("//script | //style");
                if (scriptsAndStyles != null)
                {
                    foreach (var node in scriptsAndStyles)
                    {
                        node.Remove();
                    }
                }

                return CleanText(bodyNode.InnerText);
            }

            return "";
        }

        /// <summary>
        /// 提取链接
        /// </summary>
        private List<string> ExtractLinks(HtmlDocument doc, string baseUrl)
        {
            var links = new List<string>();
            var linkNodes = doc.DocumentNode.SelectNodes("//a[@href]");

            if (linkNodes != null)
            {
                foreach (var node in linkNodes)
                {
                    var href = node.GetAttributeValue("href", "");
                    if (!string.IsNullOrEmpty(href))
                    {
                        try
                        {
                            var absoluteUrl = new Uri(new Uri(baseUrl), href).ToString();
                            if (!links.Contains(absoluteUrl))
                            {
                                links.Add(absoluteUrl);
                            }
                        }
                        catch
                        {
                            // 忽略无效的URL
                        }
                    }
                }
            }

            return links;
        }

        /// <summary>
        /// 提取图片
        /// </summary>
        private List<string> ExtractImages(HtmlDocument doc, string baseUrl)
        {
            var images = new List<string>();
            var imgNodes = doc.DocumentNode.SelectNodes("//img[@src]");

            if (imgNodes != null)
            {
                foreach (var node in imgNodes)
                {
                    var src = node.GetAttributeValue("src", "");
                    if (!string.IsNullOrEmpty(src))
                    {
                        try
                        {
                            var absoluteUrl = new Uri(new Uri(baseUrl), src).ToString();
                            if (!images.Contains(absoluteUrl))
                            {
                                images.Add(absoluteUrl);
                            }
                        }
                        catch
                        {
                            // 忽略无效的URL
                        }
                    }
                }
            }

            return images;
        }

        /// <summary>
        /// 提取元数据
        /// </summary>
        private Dictionary<string, string> ExtractMetaData(HtmlDocument doc)
        {
            var metaData = new Dictionary<string, string>();

            // 提取meta标签
            var metaNodes = doc.DocumentNode.SelectNodes("//meta");
            if (metaNodes != null)
            {
                foreach (var node in metaNodes)
                {
                    var name = node.GetAttributeValue("name", "") ?? node.GetAttributeValue("property", "");
                    var content = node.GetAttributeValue("content", "");

                    if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(content))
                    {
                        metaData[name] = content;
                    }
                }
            }

            return metaData;
        }

        /// <summary>
        /// 清理文本
        /// </summary>
        private string CleanText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            // 移除多余的空白字符
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ");
            
            // 移除HTML实体
            text = System.Net.WebUtility.HtmlDecode(text);
            
            return text.Trim();
        }

        /// <summary>
        /// 统计字数
        /// </summary>
        private int CountWords(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // 简单的字数统计（按空格分割）
            var words = text.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            return words.Length;
        }

        /// <summary>
        /// 批量爬取多个URL
        /// </summary>
        public async Task<List<ScrapingResult>> ScrapeMultipleUrlsAsync(List<string> urls, ScrapingRequest baseRequest)
        {
            var results = new List<ScrapingResult>();
            var tasks = new List<Task<ScrapingResult>>();

            foreach (var url in urls)
            {
                var request = new ScrapingRequest
                {
                    Url = url,
                    ContentSelector = baseRequest.ContentSelector,
                    ExtractLinks = baseRequest.ExtractLinks,
                    ExtractImages = baseRequest.ExtractImages,
                    GenerateSummary = baseRequest.GenerateSummary
                };

                tasks.Add(ScrapeWebPageAsync(request));
            }

            try
            {
                var taskResults = await Task.WhenAll(tasks);
                results.AddRange(taskResults);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量爬取失败");
            }

            return results;
        }

        /// <summary>
        /// 检查URL是否可访问
        /// </summary>
        public async Task<bool> IsUrlAccessibleAsync(string url)
        {
            try
            {
                var response = await _httpClient.GetAsync(url);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }
    }
}
