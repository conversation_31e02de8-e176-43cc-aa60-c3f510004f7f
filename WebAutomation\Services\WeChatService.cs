using System.Text.RegularExpressions;
using HtmlAgilityPack;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    /// <summary>
    /// 微信网页版服务
    /// </summary>
    public class WeChatService
    {
        private readonly ChromeService _chromeService;
        private readonly HttpClient _httpClient;
        private readonly ILogger<WeChatService> _logger;
        private bool _isLoggedIn = false;
        private const string WECHAT_WEB_URL = "https://wx.qq.com/";

        public WeChatService(ChromeService chromeService, HttpClient httpClient, ILogger<WeChatService> logger)
        {
            _chromeService = chromeService;
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// 登录微信网页版
        /// </summary>
        public async Task<bool> LoginAsync()
        {
            try
            {
                _logger.LogInformation("开始微信网页版登录流程");

                // 导航到微信网页版
                var navigateSuccess = await _chromeService.NavigateToAsync(WECHAT_WEB_URL);
                if (!navigateSuccess)
                {
                    _logger.LogError("无法访问微信网页版");
                    return false;
                }

                // 等待二维码加载
                await Task.Delay(3000);

                // 检查是否已经登录
                if (await CheckLoginStatusAsync())
                {
                    _isLoggedIn = true;
                    _logger.LogInformation("微信已登录");
                    return true;
                }

                _logger.LogInformation("请扫描二维码登录微信");

                // 等待用户扫码登录（最多等待2分钟）
                var loginTimeout = DateTime.Now.AddMinutes(2);
                while (DateTime.Now < loginTimeout)
                {
                    await Task.Delay(5000);
                    
                    if (await CheckLoginStatusAsync())
                    {
                        _isLoggedIn = true;
                        _logger.LogInformation("微信登录成功");
                        return true;
                    }
                }

                _logger.LogWarning("微信登录超时");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登录失败");
                return false;
            }
        }

        /// <summary>
        /// 检查登录状态
        /// </summary>
        private async Task<bool> CheckLoginStatusAsync()
        {
            try
            {
                // 检查是否存在聊天列表元素
                var chatListExists = await _chromeService.ElementExistsAsync(".chat_list");
                var contactListExists = await _chromeService.ElementExistsAsync(".contact_list");
                
                return chatListExists || contactListExists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查登录状态失败");
                return false;
            }
        }

        /// <summary>
        /// 获取聊天列表
        /// </summary>
        public async Task<List<WeChatChat>> GetChatListAsync()
        {
            var chats = new List<WeChatChat>();

            if (!_isLoggedIn)
            {
                _logger.LogWarning("微信未登录，无法获取聊天列表");
                return chats;
            }

            try
            {
                _logger.LogInformation("获取微信聊天列表");

                // 等待聊天列表加载
                await Task.Delay(2000);

                // 执行JavaScript获取聊天列表
                var script = @"
                    var chatList = [];
                    var chatItems = document.querySelectorAll('.chat_item');
                    chatItems.forEach(function(item) {
                        var nameElement = item.querySelector('.nickname');
                        var timeElement = item.querySelector('.time');
                        var msgElement = item.querySelector('.msg');
                        
                        if (nameElement) {
                            chatList.push({
                                name: nameElement.textContent.trim(),
                                lastMessage: msgElement ? msgElement.textContent.trim() : '',
                                lastTime: timeElement ? timeElement.textContent.trim() : ''
                            });
                        }
                    });
                    return JSON.stringify(chatList);
                ";

                var result = await _chromeService.ExecuteJavaScriptAsync(script);
                if (result != null)
                {
                    var chatData = System.Text.Json.JsonSerializer.Deserialize<List<dynamic>>(result.ToString());
                    foreach (var chat in chatData)
                    {
                        chats.Add(new WeChatChat
                        {
                            Name = chat.GetProperty("name").GetString() ?? "",
                            LastMessage = chat.GetProperty("lastMessage").GetString() ?? "",
                            LastTime = chat.GetProperty("lastTime").GetString() ?? ""
                        });
                    }
                }

                _logger.LogInformation("获取到 {Count} 个聊天", chats.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取聊天列表失败");
            }

            return chats;
        }

        /// <summary>
        /// 获取消息列表
        /// </summary>
        public async Task<List<WeChatMessage>> GetMessagesAsync(int maxCount = 50)
        {
            var messages = new List<WeChatMessage>();

            if (!_isLoggedIn)
            {
                _logger.LogWarning("微信未登录，无法获取消息");
                return messages;
            }

            try
            {
                _logger.LogInformation("获取微信消息");

                // 执行JavaScript获取消息
                var script = $@"
                    var messages = [];
                    var messageItems = document.querySelectorAll('.message');
                    var count = 0;
                    
                    for (var i = messageItems.length - 1; i >= 0 && count < {maxCount}; i--) {{
                        var item = messageItems[i];
                        var senderElement = item.querySelector('.sender');
                        var contentElement = item.querySelector('.content');
                        var timeElement = item.querySelector('.time');
                        
                        if (contentElement) {{
                            messages.push({{
                                sender: senderElement ? senderElement.textContent.trim() : '未知',
                                content: contentElement.textContent.trim(),
                                timestamp: timeElement ? timeElement.textContent.trim() : new Date().toISOString()
                            }});
                            count++;
                        }}
                    }}
                    
                    return JSON.stringify(messages);
                ";

                var result = await _chromeService.ExecuteJavaScriptAsync(script);
                if (result != null)
                {
                    var messageData = System.Text.Json.JsonSerializer.Deserialize<List<dynamic>>(result.ToString());
                    foreach (var msg in messageData)
                    {
                        var timestampStr = msg.GetProperty("timestamp").GetString();
                        var timestamp = DateTime.Now;
                        if (!string.IsNullOrEmpty(timestampStr))
                        {
                            DateTime.TryParse(timestampStr, out timestamp);
                        }

                        messages.Add(new WeChatMessage
                        {
                            Sender = msg.GetProperty("sender").GetString() ?? "",
                            Content = msg.GetProperty("content").GetString() ?? "",
                            Timestamp = timestamp
                        });
                    }
                }

                _logger.LogInformation("获取到 {Count} 条消息", messages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取消息失败");
            }

            return messages;
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendMessageAsync(string chatName, string message)
        {
            if (!_isLoggedIn)
            {
                _logger.LogWarning("微信未登录，无法发送消息");
                return false;
            }

            try
            {
                _logger.LogInformation("发送消息到 {ChatName}: {Message}", chatName, message);

                // 查找并点击聊天
                var chatSelector = $"//div[contains(@class, 'chat_item')]//span[contains(text(), '{chatName}')]";
                var clickSuccess = await _chromeService.ClickElementAsync(chatSelector);
                if (!clickSuccess)
                {
                    _logger.LogError("找不到聊天: {ChatName}", chatName);
                    return false;
                }

                await Task.Delay(1000);

                // 在输入框中输入消息
                var inputSelector = ".input_area";
                var inputSuccess = await _chromeService.InputTextAsync(inputSelector, message);
                if (!inputSuccess)
                {
                    _logger.LogError("无法输入消息");
                    return false;
                }

                // 发送消息（按Enter键）
                var sendScript = @"
                    var inputArea = document.querySelector('.input_area');
                    if (inputArea) {
                        var event = new KeyboardEvent('keydown', { key: 'Enter', code: 'Enter' });
                        inputArea.dispatchEvent(event);
                    }
                ";

                await _chromeService.ExecuteJavaScriptAsync(sendScript);
                await Task.Delay(1000);

                _logger.LogInformation("消息发送成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败");
                return false;
            }
        }

        /// <summary>
        /// 搜索聊天
        /// </summary>
        public async Task<List<WeChatChat>> SearchChatsAsync(string keyword)
        {
            var results = new List<WeChatChat>();

            if (!_isLoggedIn)
            {
                _logger.LogWarning("微信未登录，无法搜索");
                return results;
            }

            try
            {
                _logger.LogInformation("搜索聊天: {Keyword}", keyword);

                // 点击搜索框
                var searchBoxSelector = ".search_bar input";
                await _chromeService.ClickElementAsync(searchBoxSelector);
                await Task.Delay(500);

                // 输入搜索关键词
                await _chromeService.InputTextAsync(searchBoxSelector, keyword);
                await Task.Delay(2000);

                // 获取搜索结果
                var searchResults = await GetChatListAsync();
                results.AddRange(searchResults.Where(c => c.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase)));

                _logger.LogInformation("搜索到 {Count} 个结果", results.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索聊天失败");
            }

            return results;
        }

        /// <summary>
        /// 获取登录状态
        /// </summary>
        public bool IsLoggedIn => _isLoggedIn;

        /// <summary>
        /// 登出
        /// </summary>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                if (_isLoggedIn)
                {
                    // 点击设置菜单
                    await _chromeService.ClickElementAsync(".header .setting");
                    await Task.Delay(1000);

                    // 点击退出登录
                    await _chromeService.ClickElementAsync(".setting_menu .logout");
                    await Task.Delay(2000);

                    _isLoggedIn = false;
                    _logger.LogInformation("微信已登出");
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登出失败");
                return false;
            }
        }

        /// <summary>
        /// 解析消息中的关键信息
        /// </summary>
        private string ExtractKeyInfo(HtmlNode messageNode)
        {
            try
            {
                // 提取文本内容
                var textContent = messageNode.InnerText?.Trim() ?? "";
                
                // 移除多余的空白字符
                textContent = Regex.Replace(textContent, @"\s+", " ");
                
                return textContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析消息失败");
                return "";
            }
        }

        /// <summary>
        /// 检查是否有新消息
        /// </summary>
        public async Task<bool> HasNewMessagesAsync()
        {
            if (!_isLoggedIn)
                return false;

            try
            {
                // 检查是否有未读消息标识
                var unreadExists = await _chromeService.ElementExistsAsync(".unread_count");
                return unreadExists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查新消息失败");
                return false;
            }
        }

        /// <summary>
        /// 获取未读消息数量
        /// </summary>
        public async Task<int> GetUnreadCountAsync()
        {
            if (!_isLoggedIn)
                return 0;

            try
            {
                var countText = await _chromeService.GetElementTextAsync(".unread_count");
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未读消息数量失败");
            }

            return 0;
        }
    }
}
