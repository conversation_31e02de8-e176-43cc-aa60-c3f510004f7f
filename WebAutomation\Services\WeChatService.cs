using WebAutomation.Models;
using HtmlAgilityPack;

namespace WebAutomation.Services
{
    /// <summary>
    /// 微信网页版服务
    /// </summary>
    public class WeChatService
    {
        private readonly ChromeService _chromeService;
        private readonly ILogger<WeChatService> _logger;
        private bool _isLoggedIn = false;
        private const string WeChatWebUrl = "https://wx.qq.com/";

        public WeChatService(ChromeService chromeService, ILogger<WeChatService> logger)
        {
            _chromeService = chromeService;
            _logger = logger;
        }

        /// <summary>
        /// 登录微信网页版
        /// </summary>
        public async Task<bool> LoginAsync()
        {
            try
            {
                _logger.LogInformation("开始登录微信网页版");
                
                // 导航到微信网页版
                var navigateSuccess = await _chromeService.NavigateToAsync(WeChatWebUrl);
                if (!navigateSuccess)
                {
                    _logger.LogError("无法访问微信网页版");
                    return false;
                }

                // 等待二维码加载
                await Task.Delay(3000);
                
                _logger.LogInformation("请扫描二维码登录微信");
                
                // 等待用户扫码登录（最多等待2分钟）
                var loginSuccess = await WaitForLoginAsync(120);
                
                if (loginSuccess)
                {
                    _isLoggedIn = true;
                    _logger.LogInformation("微信登录成功");
                    return true;
                }
                else
                {
                    _logger.LogWarning("微信登录超时或失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登录过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 等待登录完成
        /// </summary>
        private async Task<bool> WaitForLoginAsync(int timeoutSeconds)
        {
            var endTime = DateTime.Now.AddSeconds(timeoutSeconds);
            
            while (DateTime.Now < endTime)
            {
                try
                {
                    var currentUrl = _chromeService.GetCurrentUrl();
                    
                    // 检查是否已经登录成功（URL变化或出现聊天界面）
                    if (currentUrl.Contains("wx.qq.com") && !currentUrl.Contains("login"))
                    {
                        // 检查是否存在聊天列表
                        var chatListElement = await _chromeService.WaitForElementAsync(".chat_list", 5);
                        if (chatListElement != null)
                        {
                            return true;
                        }
                    }
                    
                    await Task.Delay(2000); // 每2秒检查一次
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "检查登录状态时发生错误");
                }
            }
            
            return false;
        }

        /// <summary>
        /// 获取聊天列表
        /// </summary>
        public async Task<List<string>> GetChatListAsync()
        {
            var chatList = new List<string>();
            
            try
            {
                if (!_isLoggedIn)
                {
                    _logger.LogWarning("尚未登录微信");
                    return chatList;
                }

                // 获取页面源码
                var pageSource = _chromeService.GetPageSource();
                var doc = new HtmlDocument();
                doc.LoadHtml(pageSource);

                // 查找聊天列表项
                var chatItems = doc.DocumentNode.SelectNodes("//div[contains(@class, 'chat_item')]");
                if (chatItems != null)
                {
                    foreach (var item in chatItems)
                    {
                        var nameNode = item.SelectSingleNode(".//h3[contains(@class, 'nickname')]");
                        if (nameNode != null)
                        {
                            var chatName = nameNode.InnerText?.Trim();
                            if (!string.IsNullOrEmpty(chatName))
                            {
                                chatList.Add(chatName);
                            }
                        }
                    }
                }

                _logger.LogInformation($"获取到 {chatList.Count} 个聊天");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取聊天列表失败");
            }

            return chatList;
        }

        /// <summary>
        /// 选择聊天
        /// </summary>
        public async Task<bool> SelectChatAsync(string chatName)
        {
            try
            {
                if (!_isLoggedIn)
                {
                    _logger.LogWarning("尚未登录微信");
                    return false;
                }

                // 查找并点击指定的聊天
                var chatSelector = $"//div[contains(@class, 'chat_item')]//h3[contains(text(), '{chatName}')]";
                var clickSuccess = await _chromeService.ClickElementAsync(chatSelector);
                
                if (clickSuccess)
                {
                    await Task.Delay(2000); // 等待聊天界面加载
                    _logger.LogInformation($"成功选择聊天: {chatName}");
                    return true;
                }
                else
                {
                    _logger.LogWarning($"无法找到聊天: {chatName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"选择聊天失败: {chatName}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前聊天的消息
        /// </summary>
        public async Task<List<WeChatMessage>> GetMessagesAsync(int maxCount = 50)
        {
            var messages = new List<WeChatMessage>();
            
            try
            {
                if (!_isLoggedIn)
                {
                    _logger.LogWarning("尚未登录微信");
                    return messages;
                }

                // 获取页面源码
                var pageSource = _chromeService.GetPageSource();
                var doc = new HtmlDocument();
                doc.LoadHtml(pageSource);

                // 查找消息列表
                var messageNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'message')]");
                if (messageNodes != null)
                {
                    var count = 0;
                    foreach (var messageNode in messageNodes.Take(maxCount))
                    {
                        try
                        {
                            var message = ParseMessage(messageNode);
                            if (message != null)
                            {
                                messages.Add(message);
                                count++;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "解析消息失败");
                        }
                    }
                    
                    _logger.LogInformation($"获取到 {count} 条消息");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取消息失败");
            }

            return messages;
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendMessageAsync(string message)
        {
            try
            {
                if (!_isLoggedIn)
                {
                    _logger.LogWarning("尚未登录微信");
                    return false;
                }

                // 查找输入框
                var inputSelector = "//div[contains(@class, 'input_area')]//div[@contenteditable='true']";
                var inputSuccess = await _chromeService.InputTextAsync(inputSelector, message);
                
                if (!inputSuccess)
                {
                    _logger.LogError("无法输入消息");
                    return false;
                }

                // 发送消息（按Enter或点击发送按钮）
                var sendButtonSelector = "//a[contains(@class, 'btn_send')]";
                var sendSuccess = await _chromeService.ClickElementAsync(sendButtonSelector);
                
                if (sendSuccess)
                {
                    _logger.LogInformation($"成功发送消息: {message}");
                    return true;
                }
                else
                {
                    _logger.LogError("无法发送消息");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败");
                return false;
            }
        }

        /// <summary>
        /// 解析消息节点
        /// </summary>
        private WeChatMessage? ParseMessage(HtmlNode messageNode)
        {
            try
            {
                var message = new WeChatMessage();

                // 提取发送者
                var senderNode = messageNode.SelectSingleNode(".//span[contains(@class, 'nickname')]");
                if (senderNode != null)
                {
                    message.Sender = senderNode.InnerText?.Trim() ?? "Unknown";
                }

                // 提取消息内容
                var contentNode = messageNode.SelectSingleNode(".//div[contains(@class, 'msg_content')]");
                if (contentNode != null)
                {
                    message.Content = contentNode.InnerText?.Trim() ?? "";
                }

                // 提取时间戳（如果有的话）
                var timeNode = messageNode.SelectSingleNode(".//span[contains(@class, 'time')]");
                if (timeNode != null)
                {
                    var timeText = timeNode.InnerText?.Trim();
                    if (!string.IsNullOrEmpty(timeText) && DateTime.TryParse(timeText, out DateTime timestamp))
                    {
                        message.Timestamp = timestamp;
                    }
                }

                // 检查消息类型
                var imgNode = messageNode.SelectSingleNode(".//img");
                if (imgNode != null)
                {
                    message.MessageType = "image";
                    message.ImageUrl = imgNode.GetAttributeValue("src", "");
                }

                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析消息节点失败");
                return null;
            }
        }

        /// <summary>
        /// 检查登录状态
        /// </summary>
        public bool IsLoggedIn => _isLoggedIn;

        /// <summary>
        /// 登出
        /// </summary>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                if (!_isLoggedIn)
                {
                    return true;
                }

                // 查找并点击登出按钮
                var logoutSelector = "//a[contains(@class, 'logout')]";
                var logoutSuccess = await _chromeService.ClickElementAsync(logoutSelector);
                
                if (logoutSuccess)
                {
                    _isLoggedIn = false;
                    _logger.LogInformation("微信登出成功");
                    return true;
                }
                else
                {
                    _logger.LogWarning("无法找到登出按钮");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登出失败");
                return false;
            }
        }
    }
}
