using WebAutomation.Server.Models;
using HtmlAgilityPack;
using System.Text.RegularExpressions;

namespace WebAutomation.Server.Services
{
    /// <summary>
    /// 新闻爬虫服务
    /// </summary>
    public class NewsScrapingService
    {
        private readonly ChromeService _chromeService;
        private readonly WebScrapingService _webScrapingService;
        private readonly DeepSeekService _deepSeekService;
        private readonly ILogger<NewsScrapingService> _logger;

        // 支持的新闻网站配置
        private readonly Dictionary<string, NewsSourceConfig> _newsSourceConfigs = new()
        {
            {
                "36kr",
                new NewsSourceConfig
                {
                    Name = "36氪",
                    BaseUrl = "https://36kr.com",
                    ListUrl = "https://36kr.com/newsflashes",
                    ArticleSelector = ".kr-flow-article-item",
                    TitleSelector = ".article-item-title",
                    LinkSelector = "a",
                    ContentSelector = ".kr-rich-text-wrapper"
                }
            },
            {
                "ithome",
                new NewsSourceConfig
                {
                    Name = "IT之家",
                    BaseUrl = "https://www.ithome.com",
                    ListUrl = "https://www.ithome.com/",
                    ArticleSelector = ".lst li",
                    TitleSelector = "a",
                    LinkSelector = "a",
                    ContentSelector = ".post_content"
                }
            },
            {
                "cnbeta",
                new NewsSourceConfig
                {
                    Name = "cnBeta",
                    BaseUrl = "https://www.cnbeta.com",
                    ListUrl = "https://www.cnbeta.com/",
                    ArticleSelector = ".item",
                    TitleSelector = ".title a",
                    LinkSelector = ".title a",
                    ContentSelector = ".article-content"
                }
            },
            {
                "techcrunch",
                new NewsSourceConfig
                {
                    Name = "TechCrunch",
                    BaseUrl = "https://techcrunch.com",
                    ListUrl = "https://techcrunch.com/",
                    ArticleSelector = ".post-block",
                    TitleSelector = ".post-block__title__link",
                    LinkSelector = ".post-block__title__link",
                    ContentSelector = ".article-content"
                }
            }
        };

        public NewsScrapingService(
            ChromeService chromeService,
            WebScrapingService webScrapingService,
            DeepSeekService deepSeekService,
            ILogger<NewsScrapingService> logger)
        {
            _chromeService = chromeService;
            _webScrapingService = webScrapingService;
            _deepSeekService = deepSeekService;
            _logger = logger;
        }

        /// <summary>
        /// 获取支持的新闻源列表
        /// </summary>
        public List<string> GetSupportedNewsSources()
        {
            return _newsSourceConfigs.Keys.ToList();
        }

        /// <summary>
        /// 爬取指定新闻源的文章列表
        /// </summary>
        public async Task<List<NewsArticle>> ScrapeNewsListAsync(string source, int maxArticles = 10)
        {
            var articles = new List<NewsArticle>();

            try
            {
                if (!_newsSourceConfigs.TryGetValue(source.ToLower(), out var config))
                {
                    _logger.LogError($"不支持的新闻源: {source}");
                    return articles;
                }

                _logger.LogInformation($"开始爬取 {config.Name} 的新闻列表");

                // 导航到新闻列表页
                var navigateSuccess = await _chromeService.NavigateToAsync(config.ListUrl);
                if (!navigateSuccess)
                {
                    _logger.LogError($"无法访问 {config.Name} 新闻列表页");
                    return articles;
                }

                // 等待页面加载
                await Task.Delay(3000);

                // 获取页面源码
                var pageSource = _chromeService.GetPageSource();
                var doc = new HtmlDocument();
                doc.LoadHtml(pageSource);

                // 提取文章链接
                var articleNodes = doc.DocumentNode.SelectNodes($"//{config.ArticleSelector}");
                if (articleNodes == null)
                {
                    _logger.LogWarning($"在 {config.Name} 中未找到文章节点");
                    return articles;
                }

                var count = 0;
                foreach (var articleNode in articleNodes.Take(maxArticles))
                {
                    try
                    {
                        var article = await ExtractArticleInfoAsync(articleNode, config);
                        if (article != null)
                        {
                            articles.Add(article);
                            count++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "提取文章信息失败");
                    }
                }

                _logger.LogInformation($"从 {config.Name} 成功提取 {count} 篇文章");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"爬取 {source} 新闻列表失败");
            }

            return articles;
        }

        /// <summary>
        /// 爬取单篇新闻文章的详细内容
        /// </summary>
        public async Task<NewsArticle?> ScrapeNewsArticleAsync(string url, string source = "")
        {
            try
            {
                _logger.LogInformation($"开始爬取新闻文章: {url}");

                // 使用通用爬虫服务获取内容
                var scrapingRequest = new ScrapingRequest
                {
                    Url = url,
                    IncludeImages = true,
                    IncludeLinks = false,
                    GenerateSummary = false,
                    WaitTimeSeconds = 3
                };

                var scrapingResult = await _webScrapingService.ScrapeWebPageAsync(scrapingRequest);
                
                if (!scrapingResult.IsSuccess)
                {
                    _logger.LogError($"爬取文章失败: {scrapingResult.ErrorMessage}");
                    return null;
                }

                // 创建新闻文章对象
                var article = new NewsArticle
                {
                    Title = scrapingResult.Title,
                    Content = scrapingResult.Content,
                    Url = url,
                    Source = source,
                    PublishedAt = DateTime.UtcNow,
                    ImageUrl = scrapingResult.Images.FirstOrDefault()
                };

                // 提取作者信息
                article.Author = ExtractAuthor(scrapingResult.Content);

                // 估算阅读时间
                article.ReadTime = EstimateReadingTime(scrapingResult.Content);

                // 提取标签
                article.Tags = ExtractTags(scrapingResult.Content, scrapingResult.Title);

                // 生成AI摘要
                if (!string.IsNullOrEmpty(article.Content))
                {
                    article.Summary = await _deepSeekService.GenerateTechSummaryAsync(article.Content);
                }

                _logger.LogInformation($"成功爬取文章: {article.Title}");
                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"爬取新闻文章失败: {url}");
                return null;
            }
        }

        /// <summary>
        /// 批量爬取多个新闻源的最新文章
        /// </summary>
        public async Task<List<NewsArticle>> ScrapeMultipleSourcesAsync(List<string> sources, int articlesPerSource = 5)
        {
            var allArticles = new List<NewsArticle>();

            foreach (var source in sources)
            {
                try
                {
                    var articles = await ScrapeNewsListAsync(source, articlesPerSource);
                    
                    // 为每篇文章获取详细内容
                    foreach (var article in articles.Take(3)) // 限制每个源最多3篇详细文章
                    {
                        var detailedArticle = await ScrapeNewsArticleAsync(article.Url, source);
                        if (detailedArticle != null)
                        {
                            allArticles.Add(detailedArticle);
                        }
                        
                        // 添加延迟避免过于频繁的请求
                        await Task.Delay(2000);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"爬取新闻源 {source} 失败");
                }
            }

            return allArticles;
        }

        /// <summary>
        /// 提取文章信息
        /// </summary>
        private async Task<NewsArticle?> ExtractArticleInfoAsync(HtmlNode articleNode, NewsSourceConfig config)
        {
            try
            {
                // 提取标题
                var titleNode = articleNode.SelectSingleNode($".//{config.TitleSelector}");
                if (titleNode == null)
                {
                    return null;
                }

                var title = titleNode.InnerText?.Trim();
                if (string.IsNullOrEmpty(title))
                {
                    return null;
                }

                // 提取链接
                var linkNode = articleNode.SelectSingleNode($".//{config.LinkSelector}");
                if (linkNode == null)
                {
                    return null;
                }

                var href = linkNode.GetAttributeValue("href", "");
                if (string.IsNullOrEmpty(href))
                {
                    return null;
                }

                // 转换为绝对URL
                var absoluteUrl = ConvertToAbsoluteUrl(href, config.BaseUrl);

                var article = new NewsArticle
                {
                    Title = title,
                    Url = absoluteUrl,
                    Source = config.Name,
                    PublishedAt = DateTime.UtcNow
                };

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取文章信息失败");
                return null;
            }
        }

        /// <summary>
        /// 提取作者信息
        /// </summary>
        private string ExtractAuthor(string content)
        {
            // 使用正则表达式尝试提取作者信息
            var authorPatterns = new[]
            {
                @"作者[：:]\s*([^\s\n]+)",
                @"记者[：:]\s*([^\s\n]+)",
                @"编辑[：:]\s*([^\s\n]+)",
                @"来源[：:]\s*([^\s\n]+)"
            };

            foreach (var pattern in authorPatterns)
            {
                var match = Regex.Match(content, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value.Trim();
                }
            }

            return "未知";
        }

        /// <summary>
        /// 估算阅读时间
        /// </summary>
        private int EstimateReadingTime(string content)
        {
            if (string.IsNullOrEmpty(content))
                return 0;

            // 假设中文阅读速度为每分钟300字
            var wordCount = content.Length;
            var readingTime = Math.Max(1, (int)Math.Ceiling(wordCount / 300.0));
            
            return readingTime;
        }

        /// <summary>
        /// 提取标签
        /// </summary>
        private List<string> ExtractTags(string content, string title)
        {
            var tags = new List<string>();

            // 技术相关关键词
            var techKeywords = new[]
            {
                "AI", "人工智能", "机器学习", "深度学习", "区块链", "云计算",
                "大数据", "物联网", "5G", "VR", "AR", "元宇宙", "芯片",
                "半导体", "新能源", "电动车", "自动驾驶", "量子计算"
            };

            var combinedText = $"{title} {content}".ToLower();

            foreach (var keyword in techKeywords)
            {
                if (combinedText.Contains(keyword.ToLower()))
                {
                    tags.Add(keyword);
                }
            }

            return tags.Distinct().Take(5).ToList();
        }

        /// <summary>
        /// 转换为绝对URL
        /// </summary>
        private string ConvertToAbsoluteUrl(string url, string baseUrl)
        {
            try
            {
                if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
                {
                    return url;
                }

                if (Uri.TryCreate(new Uri(baseUrl), url, out Uri? absoluteUri))
                {
                    return absoluteUri.ToString();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"转换URL失败: {url}");
            }

            return url;
        }
    }

    /// <summary>
    /// 新闻源配置
    /// </summary>
    public class NewsSourceConfig
    {
        public string Name { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string ListUrl { get; set; } = string.Empty;
        public string ArticleSelector { get; set; } = string.Empty;
        public string TitleSelector { get; set; } = string.Empty;
        public string LinkSelector { get; set; } = string.Empty;
        public string ContentSelector { get; set; } = string.Empty;
    }
}
