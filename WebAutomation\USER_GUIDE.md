# 📖 WebAutomation 用户使用指南

## 🚀 快速开始

### 1. 启动应用
```bash
cd WebAutomation
dotnet run
```
应用将在 `http://localhost:5096` 启动

### 2. 访问界面
在浏览器中打开 `http://localhost:5096`，您将看到现代化的仪表板界面。

## 🌐 Chrome浏览器控制

### 基本操作
1. **初始化浏览器**
   - 点击"初始化Chrome"按钮
   - 配置浏览器选项（无头模式、窗口大小等）
   - 等待浏览器启动完成

2. **页面导航**
   - 在"目标URL"输入框中输入网址
   - 点击"导航到页面"按钮
   - 使用前进/后退按钮控制页面

3. **元素操作**
   - 使用CSS选择器定位元素（如：`#button-id`、`.class-name`）
   - 点击"点击元素"执行点击操作
   - 输入文本到指定元素

4. **页面截图**
   - 点击"截图"按钮获取当前页面截图
   - 截图将显示在页面下方
   - 可以下载截图文件

### 高级功能
- **JavaScript执行**: 在页面中执行自定义JavaScript代码
- **元素等待**: 自动等待元素加载完成
- **批量操作**: 执行一系列自动化操作

## 🕷️ 网页爬虫

### 单页面爬取
1. **配置爬虫**
   - 输入目标URL
   - 设置内容选择器（可选，用于精确提取）
   - 选择是否提取图片、链接
   - 设置是否生成AI摘要

2. **开始爬取**
   - 点击"开始爬取"按钮
   - 等待爬取完成
   - 查看爬取结果

3. **结果管理**
   - 查看爬取的标题、内容、摘要
   - 浏览提取的链接和图片
   - 下载爬取结果

### 批量爬取
- 在爬取历史中查看所有爬取记录
- 支持多URL并发爬取
- 自动去重和数据整理

## 📰 新闻监控

### 新闻源管理
1. **选择新闻源**
   - 从下拉菜单选择新闻源（36氪、IT之家等）
   - 设置获取文章数量
   - 点击"获取新闻"

2. **批量获取**
   - 点击"批量获取所有源"
   - 一次性获取所有新闻源的最新文章
   - 自动按时间排序

### 新闻浏览
1. **新闻列表**
   - 卡片式展示新闻文章
   - 显示标题、来源、时间、摘要
   - 支持响应式布局

2. **筛选功能**
   - 按新闻源筛选
   - 关键词搜索
   - 时间范围筛选

3. **文章详情**
   - 点击"查看详情"查看完整文章
   - 支持标签分类
   - 一键跳转到原文

## 💬 微信管理

### 登录设置
1. **微信登录**
   - 点击"登录微信"按钮
   - 使用手机微信扫描二维码
   - 等待登录成功确认

2. **状态监控**
   - 实时显示登录状态
   - 自动检测连接状态
   - 支持重新登录

### 聊天管理
1. **聊天列表**
   - 查看所有聊天对话
   - 显示最后消息和时间
   - 未读消息数量提醒

2. **消息操作**
   - 选择聊天查看消息历史
   - 发送文本消息
   - 消息实时同步

3. **AI分析**
   - 对聊天内容进行智能分析
   - 提取关键信息和情感倾向
   - 生成对话摘要

## 🤖 AI智能分析

### 智能问答
1. **对话功能**
   - 在输入框中输入问题
   - AI将提供智能回答
   - 支持上下文对话

2. **问答历史**
   - 查看所有问答记录
   - 支持对话历史回顾
   - 可以继续之前的对话

### 内容分析
1. **文本摘要**
   - 输入需要摘要的内容
   - 可以自定义摘要要求
   - 生成简洁的内容摘要

2. **文本分析工具**
   - **情感分析**: 分析文本情感倾向
   - **关键词提取**: 提取重要关键词
   - **内容分类**: 自动分类文本内容
   - **多语言翻译**: 支持多种语言翻译

3. **代码生成**
   - 描述需要的功能
   - 选择编程语言
   - AI生成相应代码
   - 支持代码复制

## ⚙️ 系统设置

### 状态监控
1. **系统状态**
   - 查看Chrome、AI、微信等服务状态
   - 实时更新状态信息
   - 快速诊断问题

2. **操作日志**
   - 查看所有操作记录
   - 包含成功和失败的操作
   - 详细的错误信息

### 数据管理
1. **数据清理**
   - 清理30天前的旧数据
   - 释放存储空间
   - 保持系统性能

2. **数据导出**
   - 导出爬取结果
   - 导出新闻数据
   - 支持多种格式

3. **配置管理**
   - 设置DeepSeek API密钥
   - 配置系统参数
   - 调整性能设置

## 💡 使用技巧

### 效率提升
1. **快捷操作**
   - 使用键盘快捷键（Enter发送消息等）
   - 批量操作提高效率
   - 收藏常用配置

2. **自动化流程**
   - 组合多个功能创建工作流
   - 使用定时任务自动执行
   - 设置通知提醒

### 最佳实践
1. **Chrome控制**
   - 合理设置等待时间
   - 使用精确的CSS选择器
   - 定期清理浏览器缓存

2. **网页爬虫**
   - 遵守网站robots.txt
   - 设置合理的请求间隔
   - 处理反爬虫机制

3. **AI分析**
   - 提供清晰的问题描述
   - 合理使用API配额
   - 保存重要的分析结果

## 🔧 故障排除

### 常见问题
1. **Chrome无法启动**
   - 检查Chrome浏览器是否已安装
   - 确认ChromeDriver版本匹配
   - 尝试重新初始化

2. **爬虫失败**
   - 检查目标网站是否可访问
   - 验证CSS选择器是否正确
   - 检查网络连接

3. **AI服务离线**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API使用配额

4. **微信登录失败**
   - 确保微信网页版可用
   - 检查网络连接
   - 尝试重新扫码登录

### 性能优化
1. **系统性能**
   - 定期清理旧数据
   - 关闭不需要的功能
   - 监控内存使用

2. **网络优化**
   - 使用稳定的网络连接
   - 配置合适的超时时间
   - 避免并发过多请求

## 📞 技术支持

### 日志查看
- 在系统设置页面查看操作日志
- 检查错误信息和堆栈跟踪
- 导出日志文件进行分析

### 问题反馈
- 详细描述问题现象
- 提供操作步骤和错误信息
- 包含系统环境信息

### 功能建议
- 通过GitHub Issues提交建议
- 参与社区讨论
- 贡献代码和文档

---

**祝您使用愉快！如有问题，请查看日志或联系技术支持。** 🚀
