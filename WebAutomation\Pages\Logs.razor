@page "/logs"
@inject StateService StateService
@inject NotificationService NotificationService

<PageTitle>操作日志</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">📝 操作日志</MudText>

    <MudCard Class="mb-4">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📊 日志统计</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                         Color="Color.Secondary" 
                         OnClick="RefreshLogs">
                    刷新
                </MudButton>
                <MudButton StartIcon="Icons.Material.Filled.Delete" 
                         Color="Color.Error" 
                         OnClick="ClearLogs">
                    清空日志
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudGrid>
                <MudItem xs="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudIcon Icon="Icons.Material.Filled.List" Size="Size.Large" Color="Color.Primary" />
                        <MudText Typo="Typo.h6">@_totalLogs</MudText>
                        <MudText Typo="Typo.caption">总日志数</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudIcon Icon="Icons.Material.Filled.CheckCircle" Size="Size.Large" Color="Color.Success" />
                        <MudText Typo="Typo.h6">@_successLogs</MudText>
                        <MudText Typo="Typo.caption">成功操作</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudIcon Icon="Icons.Material.Filled.Error" Size="Size.Large" Color="Color.Error" />
                        <MudText Typo="Typo.h6">@_errorLogs</MudText>
                        <MudText Typo="Typo.caption">失败操作</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudIcon Icon="Icons.Material.Filled.Today" Size="Size.Large" Color="Color.Info" />
                        <MudText Typo="Typo.h6">@_todayLogs</MudText>
                        <MudText Typo="Typo.caption">今日操作</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📋 日志列表</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudTextField @bind-Value="_searchText" 
                            Placeholder="搜索日志..." 
                            Adornment="Adornment.Start" 
                            AdornmentIcon="Icons.Material.Filled.Search"
                            OnKeyPress="OnSearchKeyPress" />
                <MudSelect T="string" @bind-Value="_filterType" Label="筛选类型">
                    <MudSelectItem Value="@("all")">全部</MudSelectItem>
                    <MudSelectItem Value="@("success")">成功</MudSelectItem>
                    <MudSelectItem Value="@("error")">失败</MudSelectItem>
                </MudSelect>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            @if (_filteredLogs?.Count > 0)
            {
                <MudTable Items="_filteredLogs.Take(_displayCount)" 
                         Hover="true" 
                         Striped="true" 
                         Dense="true">
                    <HeaderContent>
                        <MudTh>状态</MudTh>
                        <MudTh>操作</MudTh>
                        <MudTh>详情</MudTh>
                        <MudTh>时间</MudTh>
                        <MudTh>错误信息</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <MudIcon Icon="@(context.Success ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" 
                                   Color="@(context.Success ? Color.Success : Color.Error)" />
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2">@context.Operation</MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.caption">
                                @(context.Details.Length > 50 ? context.Details.Substring(0, 50) + "..." : context.Details)
                            </MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.caption">@context.Timestamp.ToString("MM-dd HH:mm:ss")</MudText>
                        </MudTd>
                        <MudTd>
                            @if (!string.IsNullOrEmpty(context.ErrorMessage))
                            {
                                <MudTooltip Text="@context.ErrorMessage">
                                    <MudIcon Icon="Icons.Material.Filled.Warning" Color="Color.Warning" />
                                </MudTooltip>
                            }
                        </MudTd>
                    </RowTemplate>
                </MudTable>

                @if (_filteredLogs.Count > _displayCount)
                {
                    <div class="text-center mt-4">
                        <MudButton Variant="Variant.Outlined" 
                                 Color="Color.Primary" 
                                 OnClick="LoadMore">
                            加载更多 (@(_filteredLogs.Count - _displayCount) 条)
                        </MudButton>
                    </div>
                }
            }
            else
            {
                <div class="text-center pa-8">
                    <MudIcon Icon="Icons.Material.Filled.History" Size="Size.Large" Color="Color.Secondary" />
                    <MudText Typo="Typo.h6" Class="mt-4">暂无日志记录</MudText>
                    <MudText Typo="Typo.body2">系统操作日志将在这里显示</MudText>
                </div>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<OperationLog> _operationLogs = new();
    private List<OperationLog> _filteredLogs = new();
    private string _searchText = "";
    private string _filterType = "all";
    private int _displayCount = 20;
    
    private int _totalLogs = 0;
    private int _successLogs = 0;
    private int _errorLogs = 0;
    private int _todayLogs = 0;

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        await RefreshLogs();
    }

    private async Task RefreshLogs()
    {
        _operationLogs = StateService.OperationLogs;
        UpdateStatistics();
        ApplyFilters();
        StateHasChanged();
    }

    private void UpdateStatistics()
    {
        _totalLogs = _operationLogs.Count;
        _successLogs = _operationLogs.Count(l => l.Success);
        _errorLogs = _operationLogs.Count(l => !l.Success);
        _todayLogs = _operationLogs.Count(l => l.Timestamp.Date == DateTime.Today);
    }

    private void ApplyFilters()
    {
        _filteredLogs = _operationLogs.Where(log =>
        {
            var matchesSearch = string.IsNullOrEmpty(_searchText) ||
                               log.Operation.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ||
                               log.Details.Contains(_searchText, StringComparison.OrdinalIgnoreCase);

            var matchesType = _filterType == "all" ||
                             (_filterType == "success" && log.Success) ||
                             (_filterType == "error" && !log.Success);

            return matchesSearch && matchesType;
        }).OrderByDescending(l => l.Timestamp).ToList();

        _displayCount = 20; // 重置显示数量
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            ApplyFilters();
        }
    }

    private void LoadMore()
    {
        _displayCount += 20;
    }

    private async Task ClearLogs()
    {
        try
        {
            await StateService.ClearOperationLogsAsync();
            await NotificationService.ShowSuccessAsync("日志已清空");
            await RefreshLogs();
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"清空日志失败: {ex.Message}");
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
