@page "/scraping"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>网页爬虫</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">🕷️ 网页爬虫</MudText>

    <!-- 爬虫配置卡片 -->
    <MudCard Class="mb-4">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">⚙️ 爬虫配置</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudGrid>
                <MudItem xs="12" md="8">
                    <MudTextField @bind-Value="_targetUrl" 
                                Label="目标URL" 
                                Placeholder="https://example.com"
                                Adornment="Adornment.Start"
                                AdornmentIcon="Icons.Material.Filled.Language" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             FullWidth="true"
                             StartIcon="Icons.Material.Filled.PlayArrow"
                             OnClick="StartScraping"
                             Disabled="@(_isLoading || string.IsNullOrEmpty(_targetUrl))">
                        开始爬取
                    </MudButton>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_contentSelector" 
                                Label="内容选择器" 
                                Placeholder="例如: .content, #main, article"
                                HelperText="CSS选择器，用于提取特定内容" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_waitTime" 
                                   Label="等待时间(秒)" 
                                   Min="1" Max="60" />
                </MudItem>
                <MudItem xs="4">
                    <MudCheckBox @bind-Value="_extractImages" Label="提取图片" />
                </MudItem>
                <MudItem xs="4">
                    <MudCheckBox @bind-Value="_extractLinks" Label="提取链接" />
                </MudItem>
                <MudItem xs="4">
                    <MudCheckBox @bind-Value="_generateSummary" Label="生成摘要" />
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- 爬取结果 -->
    @if (_scrapingResult != null)
    {
        <MudCard Class="mb-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">📄 爬取结果</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton StartIcon="Icons.Material.Filled.Download" 
                             Color="Color.Primary" 
                             OnClick="DownloadResult">
                        下载结果
                    </MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6">@_scrapingResult.Title</MudText>
                        <MudText Typo="Typo.caption" Class="mb-2">
                            URL: @_scrapingResult.Url | 字数: @_scrapingResult.WordCount | 时间: @_scrapingResult.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                        </MudText>
                    </MudItem>
                    
                    @if (!string.IsNullOrEmpty(_scrapingResult.Summary))
                    {
                        <MudItem xs="12">
                            <MudExpansionPanels>
                                <MudExpansionPanel Text="📝 内容摘要">
                                    <div class="code-block">@_scrapingResult.Summary</div>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        </MudItem>
                    }
                    
                    <MudItem xs="12">
                        <MudExpansionPanels>
                            <MudExpansionPanel Text="📖 完整内容">
                                <div class="code-block" style="max-height: 400px; overflow-y: auto;">
                                    @_scrapingResult.Content
                                </div>
                            </MudExpansionPanel>
                        </MudExpansionPanels>
                    </MudItem>
                    
                    @if (_scrapingResult.Links?.Count > 0)
                    {
                        <MudItem xs="12" md="6">
                            <MudExpansionPanels>
                                <MudExpansionPanel Text="@($"🔗 提取的链接 ({_scrapingResult.Links.Count})")">
                                    <MudList>
                                        @foreach (var link in _scrapingResult.Links.Take(20))
                                        {
                                            <MudListItem>
                                                <MudLink Href="@link" Target="_blank">@link</MudLink>
                                            </MudListItem>
                                        }
                                        @if (_scrapingResult.Links.Count > 20)
                                        {
                                            <MudListItem>
                                                <MudText Typo="Typo.caption">... 还有 @(_scrapingResult.Links.Count - 20) 个链接</MudText>
                                            </MudListItem>
                                        }
                                    </MudList>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        </MudItem>
                    }
                    
                    @if (_scrapingResult.Images?.Count > 0)
                    {
                        <MudItem xs="12" md="6">
                            <MudExpansionPanels>
                                <MudExpansionPanel Text="@($"🖼️ 提取的图片 ({_scrapingResult.Images.Count})")">
                                    <MudList>
                                        @foreach (var image in _scrapingResult.Images.Take(10))
                                        {
                                            <MudListItem>
                                                <MudLink Href="@image" Target="_blank">@image</MudLink>
                                            </MudListItem>
                                        }
                                        @if (_scrapingResult.Images.Count > 10)
                                        {
                                            <MudListItem>
                                                <MudText Typo="Typo.caption">... 还有 @(_scrapingResult.Images.Count - 10) 张图片</MudText>
                                            </MudListItem>
                                        }
                                    </MudList>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        </MudItem>
                    }
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }

    <!-- 历史记录 -->
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📚 爬取历史</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                         Color="Color.Secondary" 
                         OnClick="LoadHistory">
                    刷新
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            @if (_scrapingHistory?.Count > 0)
            {
                <MudTable Items="_scrapingHistory" Hover="true" Striped="true">
                    <HeaderContent>
                        <MudTh>标题</MudTh>
                        <MudTh>URL</MudTh>
                        <MudTh>字数</MudTh>
                        <MudTh>时间</MudTh>
                        <MudTh>操作</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="标题">
                            <MudText>@context.Title</MudText>
                        </MudTd>
                        <MudTd DataLabel="URL">
                            <MudLink Href="@context.Url" Target="_blank">
                                @(context.Url.Length > 50 ? context.Url.Substring(0, 50) + "..." : context.Url)
                            </MudLink>
                        </MudTd>
                        <MudTd DataLabel="字数">@context.WordCount</MudTd>
                        <MudTd DataLabel="时间">@context.CreatedAt.ToString("MM-dd HH:mm")</MudTd>
                        <MudTd DataLabel="操作">
                            <MudButton Size="Size.Small" 
                                     Color="Color.Primary" 
                                     OnClick="() => ViewResult(context)">
                                查看
                            </MudButton>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            }
            else
            {
                <MudText Typo="Typo.body1" Class="text-center">暂无爬取历史</MudText>
            }
        </MudCardContent>
    </MudCard>

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

@code {
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private string _targetUrl = "";
    private string _contentSelector = "";
    private int _waitTime = 3;
    private bool _extractImages = true;
    private bool _extractLinks = true;
    private bool _generateSummary = true;
    
    private ScrapingResult? _scrapingResult;
    private List<ScrapingResult> _scrapingHistory = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadHistory();
    }

    private async Task StartScraping()
    {
        if (string.IsNullOrEmpty(_targetUrl))
        {
            await NotificationService.ShowErrorAsync("请输入目标URL");
            return;
        }

        _isLoading = true;
        _loadingMessage = "正在爬取网页内容...";
        
        try
        {
            var request = new
            {
                Url = _targetUrl,
                ContentSelector = _contentSelector,
                ExtractImages = _extractImages,
                ExtractLinks = _extractLinks,
                GenerateSummary = _generateSummary,
                WaitTimeSeconds = _waitTime
            };

            var response = await HttpClient.PostAsJsonAsync("/api/automation/scraping/webpage", request);
            
            if (response.IsSuccessStatusCode)
            {
                _scrapingResult = await response.Content.ReadFromJsonAsync<ScrapingResult>();
                await NotificationService.ShowSuccessAsync($"成功爬取网页: {_scrapingResult?.Title}");
                await LoadHistory();
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                await NotificationService.ShowErrorAsync($"爬取失败: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"爬取过程中发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadHistory()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/automation/scraping/results?count=20");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.results != null)
                {
                    _scrapingHistory = System.Text.Json.JsonSerializer.Deserialize<List<ScrapingResult>>(
                        result.results.ToString()) ?? new List<ScrapingResult>();
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"加载历史记录失败: {ex.Message}");
        }
    }

    private void ViewResult(ScrapingResult result)
    {
        _scrapingResult = result;
    }

    private async Task DownloadResult()
    {
        if (_scrapingResult != null)
        {
            await NotificationService.ShowInfoAsync("开始下载爬取结果");
            // 这里可以实现下载功能
        }
    }
}
