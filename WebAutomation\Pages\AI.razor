@page "/ai"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>AI分析</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">🤖 AI智能分析</MudText>

    <!-- AI状态卡片 -->
    <MudCard Class="mb-4">
        <MudCardContent>
            <MudGrid AlignItems="Center">
                <MudItem xs="12" md="6">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@(_aiOnline ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                 Color="@(_aiOnline ? Color.Success : Color.Error)" 
                                 Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h6">DeepSeek AI状态</MudText>
                            <MudText Typo="Typo.body2" Color="@(_aiOnline ? Color.Success : Color.Error)">
                                @(_aiOnline ? "在线" : "离线")
                            </MudText>
                        </div>
                    </div>
                </MudItem>
                <MudItem xs="12" md="6" Class="text-right">
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Primary"
                               StartIcon="Icons.Material.Filled.Refresh"
                               OnClick="CheckAIStatus"
                               Disabled="_isLoading">
                        检查状态
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <MudGrid>
        <!-- 智能问答 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4" Style="height: 500px;">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💬 智能问答</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent Style="height: 350px; overflow-y: auto;">
                    <div class="chat-container">
                        @foreach (var chat in _chatHistory)
                        {
                            <div class="chat-message mb-3">
                                <div class="user-message">
                                    <MudText Typo="Typo.body2" Class="mb-1">
                                        <strong>用户:</strong> @chat.Question
                                    </MudText>
                                </div>
                                <div class="ai-message">
                                    <MudText Typo="Typo.body2">
                                        <strong>AI:</strong> @chat.Answer
                                    </MudText>
                                </div>
                            </div>
                        }
                    </div>
                </MudCardContent>
                <MudCardActions>
                    <MudGrid AlignItems="Center">
                        <MudItem xs="10">
                            <MudTextField @bind-Value="_question" 
                                        Label="输入问题" 
                                        Placeholder="请输入您的问题..."
                                        @onkeypress="OnQuestionKeyPress" />
                        </MudItem>
                        <MudItem xs="2">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Primary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Send"
                                     OnClick="AskQuestion"
                                     Disabled="@(string.IsNullOrEmpty(_question) || _isLoading || !_aiOnline)">
                                提问
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- 内容摘要 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4" Style="height: 500px;">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📝 内容摘要</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField @bind-Value="_contentToSummarize" 
                                Label="输入内容" 
                                Lines="8"
                                Placeholder="请输入需要生成摘要的内容..."
                                Class="mb-3" />
                    
                    <MudTextField @bind-Value="_customPrompt" 
                                Label="自定义提示词（可选）" 
                                Placeholder="例如：请用100字总结以下内容的核心观点"
                                Class="mb-3" />
                    
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             FullWidth="true"
                             StartIcon="Icons.Material.Filled.Summarize"
                             OnClick="GenerateSummary"
                             Disabled="@(string.IsNullOrEmpty(_contentToSummarize) || _isLoading || !_aiOnline)">
                        生成摘要
                    </MudButton>
                    
                    @if (!string.IsNullOrEmpty(_summaryResult))
                    {
                        <MudDivider Class="my-3" />
                        <MudText Typo="Typo.subtitle2" Class="mb-2">摘要结果:</MudText>
                        <div class="code-block">
                            @_summaryResult
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 文本分析工具 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🔍 文本分析工具</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField @bind-Value="_textToAnalyze" 
                                Label="输入文本" 
                                Lines="4"
                                Placeholder="请输入需要分析的文本..."
                                Class="mb-3" />
                    
                    <MudGrid>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Info" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Psychology"
                                     OnClick="AnalyzeSentiment"
                                     Disabled="@(string.IsNullOrEmpty(_textToAnalyze) || _isLoading || !_aiOnline)">
                                情感分析
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Label"
                                     OnClick="ExtractKeywords"
                                     Disabled="@(string.IsNullOrEmpty(_textToAnalyze) || _isLoading || !_aiOnline)">
                                关键词提取
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Tertiary" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Category"
                                     OnClick="ClassifyContent"
                                     Disabled="@(string.IsNullOrEmpty(_textToAnalyze) || _isLoading || !_aiOnline)">
                                内容分类
                            </MudButton>
                        </MudItem>
                        <MudItem xs="6">
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Warning" 
                                     FullWidth="true"
                                     StartIcon="Icons.Material.Filled.Translate"
                                     OnClick="TranslateText"
                                     Disabled="@(string.IsNullOrEmpty(_textToAnalyze) || _isLoading || !_aiOnline)">
                                翻译
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                    
                    @if (!string.IsNullOrEmpty(_analysisResult))
                    {
                        <MudDivider Class="my-3" />
                        <MudText Typo="Typo.subtitle2" Class="mb-2">分析结果:</MudText>
                        <div class="code-block">
                            @_analysisResult
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 代码生成 -->
        <MudItem xs="12" md="6">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💻 代码生成</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField @bind-Value="_codeDescription" 
                                Label="功能描述" 
                                Lines="3"
                                Placeholder="请描述您需要的代码功能..."
                                Class="mb-3" />
                    
                    <MudSelect T="string" @bind-Value="_codeLanguage" Label="编程语言" Class="mb-3">
                        <MudSelectItem Value="@("CSharp")">C#</MudSelectItem>
                        <MudSelectItem Value="@("JavaScript")">JavaScript</MudSelectItem>
                        <MudSelectItem Value="@("Python")">Python</MudSelectItem>
                        <MudSelectItem Value="@("Java")">Java</MudSelectItem>
                        <MudSelectItem Value="@("TypeScript")">TypeScript</MudSelectItem>
                        <MudSelectItem Value="@("SQL")">SQL</MudSelectItem>
                    </MudSelect>
                    
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Success" 
                             FullWidth="true"
                             StartIcon="Icons.Material.Filled.Code"
                             OnClick="GenerateCode"
                             Disabled="@(string.IsNullOrEmpty(_codeDescription) || _isLoading || !_aiOnline)">
                        生成代码
                    </MudButton>
                    
                    @if (!string.IsNullOrEmpty(_generatedCode))
                    {
                        <MudDivider Class="my-3" />
                        <MudText Typo="Typo.subtitle2" Class="mb-2">生成的代码:</MudText>
                        <div class="code-block" style="max-height: 300px; overflow-y: auto;">
                            <pre><code>@_generatedCode</code></pre>
                        </div>
                        <MudButton StartIcon="Icons.Material.Filled.ContentCopy" 
                                 Color="Color.Primary" 
                                 Size="Size.Small"
                                 Class="mt-2"
                                 OnClick="CopyCode">
                            复制代码
                        </MudButton>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

<style>
    .chat-container {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .chat-message {
        padding: 8px;
        border-radius: 8px;
        background-color: rgba(var(--mud-palette-surface-rgb), 0.5);
    }
    
    .user-message {
        margin-bottom: 8px;
    }
    
    .ai-message {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.1);
        padding: 8px;
        border-radius: 4px;
    }
</style>

@code {
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private bool _aiOnline = false;
    
    // 智能问答
    private string _question = "";
    private List<ChatItem> _chatHistory = new();
    
    // 内容摘要
    private string _contentToSummarize = "";
    private string _customPrompt = "";
    private string _summaryResult = "";
    
    // 文本分析
    private string _textToAnalyze = "";
    private string _analysisResult = "";
    
    // 代码生成
    private string _codeDescription = "";
    private string _codeLanguage = "CSharp";
    private string _generatedCode = "";

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        _aiOnline = StateService.SystemStatus.AIOnline;
        await CheckAIStatus();
    }

    private async Task CheckAIStatus()
    {
        _isLoading = true;
        _loadingMessage = "正在检查AI状态...";
        
        try
        {
            var response = await HttpClient.GetAsync("/api/automation/ai/status");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                _aiOnline = result?.isOnline == true;
                await StateService.UpdateAIStatusAsync(_aiOnline);
                
                await NotificationService.ShowInfoAsync($"AI状态: {(_aiOnline ? "在线" : "离线")}");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"检查AI状态失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task AskQuestion()
    {
        if (string.IsNullOrEmpty(_question) || !_aiOnline) return;

        _isLoading = true;
        _loadingMessage = "AI正在思考...";
        
        try
        {
            // 这里应该调用AI问答API
            await Task.Delay(2000); // 模拟AI思考时间
            
            var answer = "这是一个模拟的AI回答。在实际应用中，这里会调用DeepSeek API来获取真实的AI回答。";
            
            _chatHistory.Add(new ChatItem
            {
                Question = _question,
                Answer = answer,
                Timestamp = DateTime.Now
            });
            
            _question = "";
            await NotificationService.ShowSuccessAsync("AI回答完成");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"AI问答失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task GenerateSummary()
    {
        if (string.IsNullOrEmpty(_contentToSummarize) || !_aiOnline) return;

        _isLoading = true;
        _loadingMessage = "正在生成摘要...";
        
        try
        {
            var request = new
            {
                Content = _contentToSummarize,
                CustomPrompt = _customPrompt
            };

            var response = await HttpClient.PostAsJsonAsync("/api/automation/ai/summary", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                _summaryResult = result?.summary?.ToString() ?? "摘要生成失败";
                await NotificationService.ShowSuccessAsync("摘要生成完成");
            }
            else
            {
                await NotificationService.ShowErrorAsync("摘要生成失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"生成摘要时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task AnalyzeSentiment()
    {
        await PerformTextAnalysis("sentiment", "正在分析情感...");
    }

    private async Task ExtractKeywords()
    {
        await PerformTextAnalysis("keywords", "正在提取关键词...");
    }

    private async Task ClassifyContent()
    {
        await PerformTextAnalysis("classify", "正在分类内容...");
    }

    private async Task TranslateText()
    {
        await PerformTextAnalysis("translate", "正在翻译...");
    }

    private async Task PerformTextAnalysis(string analysisType, string loadingMessage)
    {
        if (string.IsNullOrEmpty(_textToAnalyze) || !_aiOnline) return;

        _isLoading = true;
        _loadingMessage = loadingMessage;
        
        try
        {
            // 这里应该调用相应的AI分析API
            await Task.Delay(1500); // 模拟分析时间
            
            _analysisResult = $"这是{analysisType}分析的模拟结果。在实际应用中，这里会调用DeepSeek API来获取真实的分析结果。";
            await NotificationService.ShowSuccessAsync("分析完成");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"文本分析失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task GenerateCode()
    {
        if (string.IsNullOrEmpty(_codeDescription) || !_aiOnline) return;

        _isLoading = true;
        _loadingMessage = "正在生成代码...";
        
        try
        {
            // 这里应该调用代码生成API
            await Task.Delay(3000); // 模拟代码生成时间
            
            _generatedCode = $@"// 这是根据描述生成的{_codeLanguage}代码示例
// 功能描述: {_codeDescription}

public class GeneratedCode
{{
    public void ExampleMethod()
    {{
        // 在实际应用中，这里会调用DeepSeek API
        // 根据用户描述生成真实的代码
        Console.WriteLine(""Hello, World!"");
    }}
}}";
            
            await NotificationService.ShowSuccessAsync("代码生成完成");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"代码生成失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task CopyCode()
    {
        // 这里可以实现复制到剪贴板的功能
        await NotificationService.ShowSuccessAsync("代码已复制到剪贴板");
    }

    private async Task OnQuestionKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await AskQuestion();
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }

    private class ChatItem
    {
        public string Question { get; set; } = "";
        public string Answer { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}
