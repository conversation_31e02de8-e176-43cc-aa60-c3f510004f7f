@page "/about"

<PageTitle>关于系统</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">ℹ️ 关于 WebAutomation</MudText>

    <MudGrid>
        <MudItem xs="12" md="8">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🚀 系统介绍</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudText Typo="Typo.body1" Class="mb-3">
                        WebAutomation 是一个基于 Blazor Server 的现代化智能自动化平台，集成了Chrome浏览器控制、网页爬虫、微信管理、新闻监控和AI分析等功能。
                    </MudText>
                    <MudText Typo="Typo.body1" Class="mb-3">
                        本系统采用最新的 .NET 8.0 技术栈，结合 MudBlazor UI 组件库，为用户提供现代化、响应式的Web界面体验。
                    </MudText>
                    <MudText Typo="Typo.body1">
                        通过模块化的设计架构，系统支持多种自动化场景，帮助用户提高工作效率，实现智能化的内容处理和分析。
                    </MudText>
                </MudCardContent>
            </MudCard>

            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🔧 技术栈</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Code" Class="mr-3" />
                                <div>
                                    <MudText Typo="Typo.subtitle2">前端技术</MudText>
                                    <MudText Typo="Typo.body2">Blazor Server 8.0, MudBlazor 6.11.2, SignalR</MudText>
                                </div>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Storage" Class="mr-3" />
                                <div>
                                    <MudText Typo="Typo.subtitle2">后端技术</MudText>
                                    <MudText Typo="Typo.body2">ASP.NET Core 8.0, Selenium WebDriver, HtmlAgilityPack</MudText>
                                </div>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Psychology" Class="mr-3" />
                                <div>
                                    <MudText Typo="Typo.subtitle2">AI集成</MudText>
                                    <MudText Typo="Typo.body2">DeepSeek API, 智能内容分析, 自然语言处理</MudText>
                                </div>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.DataObject" Class="mr-3" />
                                <div>
                                    <MudText Typo="Typo.subtitle2">数据存储</MudText>
                                    <MudText Typo="Typo.body2">JSON文件数据库, Serilog日志系统</MudText>
                                </div>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 系统信息</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText>版本号</MudText>
                                <MudChip Size="Size.Small" Color="Color.Primary">v1.0.0</MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText>.NET版本</MudText>
                                <MudChip Size="Size.Small" Color="Color.Secondary">8.0</MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText>框架</MudText>
                                <MudChip Size="Size.Small" Color="Color.Tertiary">Blazor Server</MudChip>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex justify-space-between">
                                <MudText>UI库</MudText>
                                <MudChip Size="Size.Small" Color="Color.Info">MudBlazor</MudChip>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>

            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🎯 核心功能</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Web" Color="Color.Primary" Class="mr-2" />
                                <MudText>Chrome浏览器控制</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Search" Color="Color.Secondary" Class="mr-2" />
                                <MudText>智能网页爬虫</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Article" Color="Color.Tertiary" Class="mr-2" />
                                <MudText>新闻监控分析</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Chat" Color="Color.Success" Class="mr-2" />
                                <MudText>微信管理系统</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div class="d-flex align-center">
                                <MudIcon Icon="Icons.Material.Filled.Psychology" Color="Color.Warning" Class="mr-2" />
                                <MudText>AI智能分析</MudText>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📞 联系信息</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudText Typo="Typo.body1" Class="mb-2">
                        <MudIcon Icon="Icons.Material.Filled.Email" Class="mr-2" />
                        技术支持: <EMAIL>
                    </MudText>
                    <MudText Typo="Typo.body1" Class="mb-2">
                        <MudIcon Icon="Icons.Material.Filled.GitHub" Class="mr-2" />
                        开源地址: https://github.com/webautomation
                    </MudText>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudText Typo="Typo.body1" Class="mb-2">
                        <MudIcon Icon="Icons.Material.Filled.Description" Class="mr-2" />
                        文档地址: https://docs.webautomation.com
                    </MudText>
                    <MudText Typo="Typo.body1">
                        <MudIcon Icon="Icons.Material.Filled.Copyright" Class="mr-2" />
                        版权所有 © 2024 WebAutomation Team
                    </MudText>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>
</MudContainer>
