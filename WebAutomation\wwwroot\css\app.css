/* Blazor WebAssembly 应用样式 */

html, body {
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
    height: 100%;
    background: #f5f5f5;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 加载动画 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid rgba(255, 255, 255, 0.3);
    border-top: 6px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-container h3 {
    margin: 10px 0;
    font-size: 2rem;
    font-weight: 300;
}

.loading-container p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 错误UI */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* 自定义MudBlazor样式 */
.mud-appbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.mud-drawer {
    background: #ffffff !important;
    border-right: 1px solid #e0e0e0 !important;
}

.mud-nav-link {
    border-radius: 8px !important;
    margin: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.mud-nav-link:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateX(4px) !important;
}

.mud-nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* 卡片样式增强 */
.mud-card {
    transition: all 0.3s ease !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.mud-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 按钮样式增强 */
.mud-button-filled-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    transition: all 0.3s ease !important;
}

.mud-button-filled-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.mud-button-filled-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border: none !important;
}

.mud-button-filled-tertiary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
}

.mud-button-filled-success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    border: none !important;
}

.mud-button-filled-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
    border: none !important;
}

/* 表格样式 */
.mud-table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.mud-table-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.mud-table-head .mud-table-cell {
    color: white !important;
    font-weight: 600 !important;
}

/* 输入框样式 */
.mud-input-control {
    border-radius: 8px !important;
}

.mud-input-control:focus-within {
    border-color: #667eea !important;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 进度条样式 */
.mud-progress-linear {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 芯片样式 */
.mud-chip {
    border-radius: 16px !important;
    font-weight: 500 !important;
}

.mud-chip-color-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* 对话框样式 */
.mud-dialog {
    border-radius: 16px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
}

/* 抽屉样式 */
.mud-drawer-content {
    padding: 16px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mud-container {
        padding: 8px !important;
    }
    
    .mud-grid-item {
        margin-bottom: 16px !important;
    }
}

/* 自定义动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online {
    background: #4caf50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
}

.status-offline {
    background: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
}

.status-warning {
    background: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
}

/* 代码块样式 */
.code-block {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.mud-tooltip {
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
}

/* 徽章样式 */
.mud-badge {
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 11px !important;
}

/* 分页样式 */
.mud-pagination .mud-button {
    border-radius: 8px !important;
    margin: 0 2px !important;
}

/* 选项卡样式 */
.mud-tabs .mud-tab {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 500 !important;
}

.mud-tabs .mud-tab.mud-tab-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}
