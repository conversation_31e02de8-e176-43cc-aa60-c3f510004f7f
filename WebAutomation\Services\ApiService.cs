using System.Net.Http.Json;
using System.Text.Json;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    public class ApiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "/api/automation";

        public ApiService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        #region Chrome控制

        /// <summary>
        /// 初始化Chrome浏览器
        /// </summary>
        public async Task<ApiResponse<object>?> InitializeChromeAsync(ChromeOptions options)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/chrome/init", options);
                return await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        public async Task<ApiResponse<object>?> NavigateToUrlAsync(string url)
        {
            try
            {
                var request = new NavigateRequest { Url = url };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/chrome/navigate", request);
                return await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 点击元素
        /// </summary>
        public async Task<ApiResponse<object>?> ClickElementAsync(string selector, int timeoutSeconds = 10)
        {
            try
            {
                var request = new ElementActionRequest { Selector = selector, TimeoutSeconds = timeoutSeconds };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/chrome/click", request);
                return await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 输入文本
        /// </summary>
        public async Task<ApiResponse<object>?> InputTextAsync(string selector, string text, int timeoutSeconds = 10)
        {
            try
            {
                var request = new InputTextRequest { Selector = selector, Text = text, TimeoutSeconds = timeoutSeconds };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/chrome/input", request);
                return await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 获取截图
        /// </summary>
        public async Task<byte[]?> GetScreenshotAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/chrome/screenshot");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region 网页爬虫

        /// <summary>
        /// 爬取网页内容
        /// </summary>
        public async Task<ScrapingResult?> ScrapeWebPageAsync(ScrapingRequest request)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/scraping/webpage", request);
                return await response.Content.ReadFromJsonAsync<ScrapingResult>();
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region 新闻爬虫

        /// <summary>
        /// 获取支持的新闻源
        /// </summary>
        public async Task<NewsSourcesResponse?> GetNewsSourcesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/news/sources");
                return await response.Content.ReadFromJsonAsync<NewsSourcesResponse>();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 爬取新闻列表
        /// </summary>
        public async Task<List<NewsArticle>?> ScrapeNewsListAsync(string source, int maxArticles = 10)
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/news/{source}?maxArticles={maxArticles}");
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<NewsArticle>>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 爬取单篇新闻文章
        /// </summary>
        public async Task<NewsArticle?> ScrapeNewsArticleAsync(string url, string source = "")
        {
            try
            {
                var request = new NewsArticleRequest { Url = url, Source = source };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/news/article", request);
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<NewsArticle>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 批量爬取多个新闻源
        /// </summary>
        public async Task<List<NewsArticle>?> ScrapeMultipleSourcesAsync(List<string> sources, int articlesPerSource = 5)
        {
            try
            {
                var request = new BatchNewsRequest { Sources = sources, ArticlesPerSource = articlesPerSource };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/news/batch", request);
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<NewsArticle>>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region 微信操作

        /// <summary>
        /// 微信登录
        /// </summary>
        public async Task<ApiResponse<object>?> WeChatLoginAsync()
        {
            try
            {
                var response = await _httpClient.PostAsync($"{_baseUrl}/wechat/login", null);
                return await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 获取微信聊天列表
        /// </summary>
        public async Task<List<string>?> GetWeChatChatsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/wechat/chats");
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<string>>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取微信消息
        /// </summary>
        public async Task<List<WeChatMessage>?> GetWeChatMessagesAsync(int maxCount = 50)
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/wechat/messages?maxCount={maxCount}");
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<WeChatMessage>>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 分析微信消息
        /// </summary>
        public async Task<string?> AnalyzeWeChatMessagesAsync(List<WeChatMessage> messages)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/wechat/analyze", messages);
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<string>>();
                return result?.Data;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region AI分析

        /// <summary>
        /// 检查AI状态
        /// </summary>
        public async Task<AIStatusResponse?> CheckAIStatusAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/ai/status");
                return await response.Content.ReadFromJsonAsync<AIStatusResponse>();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 生成内容摘要
        /// </summary>
        public async Task<string?> GenerateSummaryAsync(string content, string? customPrompt = null)
        {
            try
            {
                var request = new SummaryRequest { Content = content, CustomPrompt = customPrompt };
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/ai/summary", request);
                var result = await response.Content.ReadFromJsonAsync<SummaryResponse>();
                return result?.Summary;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region 系统管理

        /// <summary>
        /// 检查系统健康状态
        /// </summary>
        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
