using WebAutomation.Services;
using WebAutomation.Models;
using Microsoft.Extensions.Logging;

namespace WebAutomation
{
    public class TestScraping
    {
        public static async Task Main(string[] args)
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
            });
            var logger = loggerFactory.CreateLogger<WebScrapingService>();

            // 创建HttpClient
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

            // 创建爬虫服务
            var scrapingService = new WebScrapingService(httpClient, logger);

            // 创建爬取请求
            var request = new ScrapingRequest
            {
                Url = "https://www.163.com",
                ExtractLinks = true,
                ExtractImages = false,
                GenerateSummary = true
            };

            Console.WriteLine("开始爬取 163.com...");

            try
            {
                // 执行爬取
                var result = await scrapingService.ScrapeWebPageAsync(request);

                if (result.IsSuccess)
                {
                    Console.WriteLine($"爬取成功！");
                    Console.WriteLine($"标题: {result.Title}");
                    Console.WriteLine($"字数: {result.WordCount}");
                    Console.WriteLine($"链接数量: {result.Links?.Count ?? 0}");
                    Console.WriteLine($"图片数量: {result.Images?.Count ?? 0}");
                    
                    Console.WriteLine("\n=== 内容摘要 ===");
                    if (!string.IsNullOrEmpty(result.Content))
                    {
                        // 简单的内容摘要（前500字符）
                        var summary = result.Content.Length > 500 ? 
                            result.Content.Substring(0, 500) + "..." : 
                            result.Content;
                        Console.WriteLine(summary);
                    }

                    Console.WriteLine("\n=== 科技相关链接 ===");
                    if (result.Links != null)
                    {
                        var techLinks = result.Links
                            .Where(link => link.Contains("tech", StringComparison.OrdinalIgnoreCase) ||
                                          link.Contains("科技", StringComparison.OrdinalIgnoreCase) ||
                                          link.Contains("数码", StringComparison.OrdinalIgnoreCase) ||
                                          link.Contains("ai", StringComparison.OrdinalIgnoreCase) ||
                                          link.Contains("mobile", StringComparison.OrdinalIgnoreCase))
                            .Take(10)
                            .ToList();

                        foreach (var link in techLinks)
                        {
                            Console.WriteLine($"- {link}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"爬取失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
