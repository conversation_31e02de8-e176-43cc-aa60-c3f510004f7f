using HtmlAgilityPack;
using WebAutomation.Server.Models;
using System.Text.RegularExpressions;

namespace WebAutomation.Server.Services
{
    /// <summary>
    /// 网页爬虫服务
    /// </summary>
    public class WebScrapingService
    {
        private readonly ChromeService _chromeService;
        private readonly ILogger<WebScrapingService> _logger;
        private readonly HttpClient _httpClient;

        public WebScrapingService(ChromeService chromeService, ILogger<WebScrapingService> logger, HttpClient httpClient)
        {
            _chromeService = chromeService;
            _logger = logger;
            _httpClient = httpClient;
        }

        /// <summary>
        /// 爬取网页内容
        /// </summary>
        public async Task<ScrapingResult> ScrapeWebPageAsync(ScrapingRequest request)
        {
            var result = new ScrapingResult
            {
                Url = request.Url,
                Source = "WebScraping"
            };

            try
            {
                // 使用Chrome浏览器加载页面
                var navigateSuccess = await _chromeService.NavigateToAsync(request.Url);
                if (!navigateSuccess)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "无法导航到指定URL";
                    return result;
                }

                // 等待页面加载
                await Task.Delay(request.WaitTimeSeconds * 1000);

                // 获取页面源码
                var pageSource = _chromeService.GetPageSource();
                result.Title = _chromeService.GetPageTitle();

                // 解析HTML
                var doc = new HtmlDocument();
                doc.LoadHtml(pageSource);

                // 提取主要内容
                result.Content = ExtractMainContent(doc, request.CustomSelector);

                // 提取图片链接
                if (request.IncludeImages)
                {
                    result.Images = ExtractImages(doc, request.Url);
                }

                // 提取链接
                if (request.IncludeLinks)
                {
                    result.Links = ExtractLinks(doc, request.Url);
                }

                // 添加元数据
                result.Metadata = ExtractMetadata(doc);

                _logger.LogInformation($"成功爬取网页: {request.Url}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"爬取网页失败: {request.Url}");
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 提取主要内容
        /// </summary>
        private string ExtractMainContent(HtmlDocument doc, string? customSelector = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(customSelector))
                {
                    var customNode = doc.DocumentNode.SelectSingleNode(customSelector);
                    if (customNode != null)
                    {
                        return CleanText(customNode.InnerText);
                    }
                }

                // 尝试常见的内容选择器
                var contentSelectors = new[]
                {
                    "article",
                    ".article-content",
                    ".post-content",
                    ".content",
                    ".main-content",
                    "#content",
                    ".entry-content",
                    ".post-body",
                    "main"
                };

                foreach (var selector in contentSelectors)
                {
                    var node = doc.DocumentNode.SelectSingleNode($"//{selector.TrimStart('.')}[contains(@class, '{selector.TrimStart('.')}')]") ??
                              doc.DocumentNode.SelectSingleNode($"//{selector.TrimStart('#')}[@id='{selector.TrimStart('#')}']") ??
                              doc.DocumentNode.SelectSingleNode($"//{selector}");

                    if (node != null && !string.IsNullOrWhiteSpace(node.InnerText))
                    {
                        return CleanText(node.InnerText);
                    }
                }

                // 如果没有找到特定内容区域，提取body中的文本
                var bodyNode = doc.DocumentNode.SelectSingleNode("//body");
                if (bodyNode != null)
                {
                    // 移除脚本和样式标签
                    var scriptsAndStyles = bodyNode.SelectNodes(".//script | .//style | .//nav | .//header | .//footer");
                    if (scriptsAndStyles != null)
                    {
                        foreach (var node in scriptsAndStyles)
                        {
                            node.Remove();
                        }
                    }

                    return CleanText(bodyNode.InnerText);
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取主要内容失败");
                return string.Empty;
            }
        }

        /// <summary>
        /// 提取图片链接
        /// </summary>
        private List<string> ExtractImages(HtmlDocument doc, string baseUrl)
        {
            var images = new List<string>();
            
            try
            {
                var imgNodes = doc.DocumentNode.SelectNodes("//img[@src]");
                if (imgNodes != null)
                {
                    foreach (var img in imgNodes)
                    {
                        var src = img.GetAttributeValue("src", "");
                        if (!string.IsNullOrEmpty(src))
                        {
                            var absoluteUrl = ConvertToAbsoluteUrl(src, baseUrl);
                            if (!string.IsNullOrEmpty(absoluteUrl) && !images.Contains(absoluteUrl))
                            {
                                images.Add(absoluteUrl);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取图片链接失败");
            }

            return images;
        }

        /// <summary>
        /// 提取链接
        /// </summary>
        private List<string> ExtractLinks(HtmlDocument doc, string baseUrl)
        {
            var links = new List<string>();
            
            try
            {
                var linkNodes = doc.DocumentNode.SelectNodes("//a[@href]");
                if (linkNodes != null)
                {
                    foreach (var link in linkNodes)
                    {
                        var href = link.GetAttributeValue("href", "");
                        if (!string.IsNullOrEmpty(href) && !href.StartsWith("#") && !href.StartsWith("javascript:"))
                        {
                            var absoluteUrl = ConvertToAbsoluteUrl(href, baseUrl);
                            if (!string.IsNullOrEmpty(absoluteUrl) && !links.Contains(absoluteUrl))
                            {
                                links.Add(absoluteUrl);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取链接失败");
            }

            return links;
        }

        /// <summary>
        /// 提取元数据
        /// </summary>
        private Dictionary<string, object> ExtractMetadata(HtmlDocument doc)
        {
            var metadata = new Dictionary<string, object>();
            
            try
            {
                // 提取meta标签
                var metaNodes = doc.DocumentNode.SelectNodes("//meta");
                if (metaNodes != null)
                {
                    foreach (var meta in metaNodes)
                    {
                        var name = meta.GetAttributeValue("name", "") ?? meta.GetAttributeValue("property", "");
                        var content = meta.GetAttributeValue("content", "");
                        
                        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(content))
                        {
                            metadata[name] = content;
                        }
                    }
                }

                // 提取其他有用信息
                var titleNode = doc.DocumentNode.SelectSingleNode("//title");
                if (titleNode != null)
                {
                    metadata["title"] = titleNode.InnerText;
                }

                var h1Nodes = doc.DocumentNode.SelectNodes("//h1");
                if (h1Nodes != null && h1Nodes.Count > 0)
                {
                    metadata["h1_count"] = h1Nodes.Count;
                    metadata["first_h1"] = h1Nodes[0].InnerText;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取元数据失败");
            }

            return metadata;
        }

        /// <summary>
        /// 清理文本
        /// </summary>
        private string CleanText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // 移除多余的空白字符
            text = Regex.Replace(text, @"\s+", " ");
            
            // 移除首尾空白
            text = text.Trim();
            
            return text;
        }

        /// <summary>
        /// 转换为绝对URL
        /// </summary>
        private string ConvertToAbsoluteUrl(string url, string baseUrl)
        {
            try
            {
                if (Uri.IsWellFormedUriString(url, UriKind.Absolute))
                {
                    return url;
                }

                if (Uri.TryCreate(new Uri(baseUrl), url, out Uri? absoluteUri))
                {
                    return absoluteUri.ToString();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"转换URL失败: {url}");
            }

            return string.Empty;
        }
    }
}
