@page "/test"

<PageTitle>测试页面</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">🧪 系统测试</MudText>
    
    <MudCard>
        <MudCardContent>
            <MudText Typo="Typo.h6">WebAutomation 系统测试</MudText>
            <MudText Typo="Typo.body1" Class="mt-2">
                如果您能看到这个页面，说明Blazor Server应用已经成功运行！
            </MudText>
            
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary" 
                       Class="mt-4"
                       OnClick="ShowMessage">
                测试按钮
            </MudButton>
            
            @if (!string.IsNullOrEmpty(_message))
            {
                <MudAlert Severity="Severity.Success" Class="mt-4">
                    @_message
                </MudAlert>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private string _message = "";

    private void ShowMessage()
    {
        _message = $"测试成功！当前时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}";
    }
}
