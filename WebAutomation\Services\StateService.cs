using Blazored.LocalStorage;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    public class StateService
    {
        private readonly ILocalStorageService _localStorage;
        
        public event Action? OnStateChanged;

        // 系统状态
        public SystemStatus SystemStatus { get; private set; } = new SystemStatus();
        
        // 操作日志
        public List<OperationLog> OperationLogs { get; private set; } = new List<OperationLog>();
        
        // 自动化脚本
        public List<AutomationScript> AutomationScripts { get; private set; } = new List<AutomationScript>();
        
        // 定时任务
        public List<ScheduledTask> ScheduledTasks { get; private set; } = new List<ScheduledTask>();

        public StateService(ILocalStorageService localStorage)
        {
            _localStorage = localStorage;
        }

        #region 系统状态管理

        /// <summary>
        /// 更新系统状态
        /// </summary>
        public async Task UpdateSystemStatusAsync(SystemStatus status)
        {
            SystemStatus = status;
            await _localStorage.SetItemAsync("systemStatus", status);
            NotifyStateChanged();
        }

        /// <summary>
        /// 加载系统状态
        /// </summary>
        public async Task LoadSystemStatusAsync()
        {
            try
            {
                var status = await _localStorage.GetItemAsync<SystemStatus>("systemStatus");
                if (status != null)
                {
                    SystemStatus = status;
                }
            }
            catch
            {
                SystemStatus = new SystemStatus();
            }
            NotifyStateChanged();
        }

        /// <summary>
        /// 更新Chrome连接状态
        /// </summary>
        public async Task UpdateChromeStatusAsync(bool connected)
        {
            SystemStatus.ChromeConnected = connected;
            SystemStatus.LastUpdated = DateTime.UtcNow;
            await _localStorage.SetItemAsync("systemStatus", SystemStatus);
            NotifyStateChanged();
        }

        /// <summary>
        /// 更新AI服务状态
        /// </summary>
        public async Task UpdateAIStatusAsync(bool online)
        {
            SystemStatus.AIOnline = online;
            SystemStatus.LastUpdated = DateTime.UtcNow;
            await _localStorage.SetItemAsync("systemStatus", SystemStatus);
            NotifyStateChanged();
        }

        /// <summary>
        /// 更新微信登录状态
        /// </summary>
        public async Task UpdateWeChatStatusAsync(bool loggedIn)
        {
            SystemStatus.WeChatLoggedIn = loggedIn;
            SystemStatus.LastUpdated = DateTime.UtcNow;
            await _localStorage.SetItemAsync("systemStatus", SystemStatus);
            NotifyStateChanged();
        }

        #endregion

        #region 操作日志管理

        /// <summary>
        /// 添加操作日志
        /// </summary>
        public async Task AddOperationLogAsync(string operation, string details, bool success, string? errorMessage = null)
        {
            var log = new OperationLog
            {
                Operation = operation,
                Details = details,
                Success = success,
                ErrorMessage = errorMessage
            };

            OperationLogs.Insert(0, log); // 最新的在前面
            
            // 只保留最近1000条日志
            if (OperationLogs.Count > 1000)
            {
                OperationLogs = OperationLogs.Take(1000).ToList();
            }

            await _localStorage.SetItemAsync("operationLogs", OperationLogs);
            NotifyStateChanged();
        }

        /// <summary>
        /// 加载操作日志
        /// </summary>
        public async Task LoadOperationLogsAsync()
        {
            try
            {
                var logs = await _localStorage.GetItemAsync<List<OperationLog>>("operationLogs");
                if (logs != null)
                {
                    OperationLogs = logs;
                }
            }
            catch
            {
                OperationLogs = new List<OperationLog>();
            }
            NotifyStateChanged();
        }

        /// <summary>
        /// 清空操作日志
        /// </summary>
        public async Task ClearOperationLogsAsync()
        {
            OperationLogs.Clear();
            await _localStorage.RemoveItemAsync("operationLogs");
            NotifyStateChanged();
        }

        #endregion

        #region 自动化脚本管理

        /// <summary>
        /// 添加自动化脚本
        /// </summary>
        public async Task AddAutomationScriptAsync(AutomationScript script)
        {
            AutomationScripts.Add(script);
            await _localStorage.SetItemAsync("automationScripts", AutomationScripts);
            NotifyStateChanged();
        }

        /// <summary>
        /// 更新自动化脚本
        /// </summary>
        public async Task UpdateAutomationScriptAsync(AutomationScript script)
        {
            var index = AutomationScripts.FindIndex(s => s.Id == script.Id);
            if (index >= 0)
            {
                AutomationScripts[index] = script;
                await _localStorage.SetItemAsync("automationScripts", AutomationScripts);
                NotifyStateChanged();
            }
        }

        /// <summary>
        /// 删除自动化脚本
        /// </summary>
        public async Task DeleteAutomationScriptAsync(string scriptId)
        {
            AutomationScripts.RemoveAll(s => s.Id == scriptId);
            await _localStorage.SetItemAsync("automationScripts", AutomationScripts);
            NotifyStateChanged();
        }

        /// <summary>
        /// 加载自动化脚本
        /// </summary>
        public async Task LoadAutomationScriptsAsync()
        {
            try
            {
                var scripts = await _localStorage.GetItemAsync<List<AutomationScript>>("automationScripts");
                if (scripts != null)
                {
                    AutomationScripts = scripts;
                }
            }
            catch
            {
                AutomationScripts = new List<AutomationScript>();
            }
            NotifyStateChanged();
        }

        #endregion

        #region 定时任务管理

        /// <summary>
        /// 添加定时任务
        /// </summary>
        public async Task AddScheduledTaskAsync(ScheduledTask task)
        {
            ScheduledTasks.Add(task);
            await _localStorage.SetItemAsync("scheduledTasks", ScheduledTasks);
            NotifyStateChanged();
        }

        /// <summary>
        /// 更新定时任务
        /// </summary>
        public async Task UpdateScheduledTaskAsync(ScheduledTask task)
        {
            var index = ScheduledTasks.FindIndex(t => t.Id == task.Id);
            if (index >= 0)
            {
                ScheduledTasks[index] = task;
                await _localStorage.SetItemAsync("scheduledTasks", ScheduledTasks);
                NotifyStateChanged();
            }
        }

        /// <summary>
        /// 删除定时任务
        /// </summary>
        public async Task DeleteScheduledTaskAsync(string taskId)
        {
            ScheduledTasks.RemoveAll(t => t.Id == taskId);
            await _localStorage.SetItemAsync("scheduledTasks", ScheduledTasks);
            NotifyStateChanged();
        }

        /// <summary>
        /// 加载定时任务
        /// </summary>
        public async Task LoadScheduledTasksAsync()
        {
            try
            {
                var tasks = await _localStorage.GetItemAsync<List<ScheduledTask>>("scheduledTasks");
                if (tasks != null)
                {
                    ScheduledTasks = tasks;
                }
            }
            catch
            {
                ScheduledTasks = new List<ScheduledTask>();
            }
            NotifyStateChanged();
        }

        #endregion

        #region 通用方法

        /// <summary>
        /// 初始化状态服务
        /// </summary>
        public async Task InitializeAsync()
        {
            await LoadSystemStatusAsync();
            await LoadOperationLogsAsync();
            await LoadAutomationScriptsAsync();
            await LoadScheduledTasksAsync();
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public async Task ClearAllDataAsync()
        {
            await _localStorage.ClearAsync();
            SystemStatus = new SystemStatus();
            OperationLogs.Clear();
            AutomationScripts.Clear();
            ScheduledTasks.Clear();
            NotifyStateChanged();
        }

        /// <summary>
        /// 通知状态变化
        /// </summary>
        private void NotifyStateChanged()
        {
            OnStateChanged?.Invoke();
        }

        #endregion
    }
}
