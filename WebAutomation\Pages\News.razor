@page "/news"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>新闻监控</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">📰 新闻监控</MudText>

    <!-- 新闻源选择 -->
    <MudCard Class="mb-4">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📡 新闻源配置</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudSelect T="string" @bind-Value="_selectedSource" Label="选择新闻源">
                        @foreach (var source in _newsSources)
                        {
                            <MudSelectItem Value="@source">@source</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudNumericField @bind-Value="_articleCount" 
                                   Label="文章数量" 
                                   Min="1" Max="50" />
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             FullWidth="true"
                             StartIcon="Icons.Material.Filled.Download"
                             OnClick="FetchNews"
                             Disabled="@(_isLoading || string.IsNullOrEmpty(_selectedSource))">
                        获取新闻
                    </MudButton>
                </MudItem>
                <MudItem xs="12">
                    <MudButton Variant="Variant.Outlined" 
                             Color="Color.Secondary" 
                             StartIcon="Icons.Material.Filled.Sync"
                             OnClick="FetchAllSources"
                             Disabled="_isLoading">
                        批量获取所有源
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- 新闻列表 -->
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">📋 新闻列表 (@_newsArticles.Count)</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                         Color="Color.Secondary" 
                         OnClick="LoadNewsHistory">
                    刷新
                </MudButton>
                <MudButton StartIcon="Icons.Material.Filled.FilterList" 
                         Color="Color.Info" 
                         OnClick="ToggleFilter">
                    筛选
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <!-- 筛选器 -->
            @if (_showFilter)
            {
                <MudGrid Class="mb-4">
                    <MudItem xs="12" md="4">
                        <MudSelect T="string" @bind-Value="_filterSource" Label="筛选来源" Clearable="true">
                            @foreach (var source in _newsSources)
                            {
                                <MudSelectItem Value="@source">@source</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudTextField @bind-Value="_searchKeyword" 
                                    Label="关键词搜索" 
                                    Placeholder="搜索标题或内容"
                                    Clearable="true" />
                    </MudItem>
                    <MudItem xs="12" md="4">
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 OnClick="ApplyFilter">
                            应用筛选
                        </MudButton>
                    </MudItem>
                </MudGrid>
                <MudDivider Class="mb-4" />
            }

            @if (_filteredArticles?.Count > 0)
            {
                <MudGrid>
                    @foreach (var article in _filteredArticles.Take(_displayCount))
                    {
                        <MudItem xs="12" md="6" lg="4">
                            <MudCard Class="news-card" Style="height: 300px;">
                                <MudCardContent>
                                    <MudText Typo="Typo.h6" Class="news-title">
                                        @article.Title
                                    </MudText>
                                    <MudText Typo="Typo.caption" Class="mb-2">
                                        <MudChip Size="Size.Small" Color="Color.Primary">@article.Source</MudChip>
                                        @article.PublishedAt.ToString("MM-dd HH:mm")
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="news-summary">
                                        @(string.IsNullOrEmpty(article.Summary) ? 
                                          (article.Content.Length > 100 ? article.Content.Substring(0, 100) + "..." : article.Content) : 
                                          article.Summary)
                                    </MudText>
                                </MudCardContent>
                                <MudCardActions>
                                    <MudButton Size="Size.Small" 
                                             Color="Color.Primary" 
                                             OnClick="() => ViewArticle(article)">
                                        查看详情
                                    </MudButton>
                                    <MudButton Size="Size.Small" 
                                             Color="Color.Secondary" 
                                             Href="@article.Url" 
                                             Target="_blank">
                                        原文链接
                                    </MudButton>
                                </MudCardActions>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>

                @if (_filteredArticles.Count > _displayCount)
                {
                    <div class="text-center mt-4">
                        <MudButton Variant="Variant.Outlined" 
                                 Color="Color.Primary" 
                                 OnClick="LoadMore">
                            加载更多 (@(_filteredArticles.Count - _displayCount) 篇)
                        </MudButton>
                    </div>
                }
            }
            else
            {
                <MudText Typo="Typo.body1" Class="text-center">
                    @(_isLoading ? "正在加载新闻..." : "暂无新闻数据")
                </MudText>
            }
        </MudCardContent>
    </MudCard>

    <!-- 文章详情对话框 -->
    <MudDialog @bind-IsVisible="_showArticleDialog" Options="_dialogOptions">
        <TitleContent>
            <MudText Typo="Typo.h6">📖 文章详情</MudText>
        </TitleContent>
        <DialogContent>
            @if (_selectedArticle != null)
            {
                <MudText Typo="Typo.h5" Class="mb-2">@_selectedArticle.Title</MudText>
                <MudText Typo="Typo.caption" Class="mb-4">
                    来源: @_selectedArticle.Source | 
                    时间: @_selectedArticle.PublishedAt.ToString("yyyy-MM-dd HH:mm:ss") |
                    作者: @(_selectedArticle.Author ?? "未知")
                </MudText>
                
                @if (_selectedArticle.Tags?.Count > 0)
                {
                    <div class="mb-3">
                        @foreach (var tag in _selectedArticle.Tags)
                        {
                            <MudChip Size="Size.Small" Color="Color.Info" Class="mr-1">@tag</MudChip>
                        }
                    </div>
                }
                
                <MudText Typo="Typo.body1" Style="line-height: 1.6;">
                    @_selectedArticle.Content
                </MudText>
            }
        </DialogContent>
        <DialogActions>
            <MudButton Color="Color.Secondary" OnClick="CloseArticleDialog">关闭</MudButton>
            <MudButton Color="Color.Primary" 
                     Href="@_selectedArticle?.Url" 
                     Target="_blank">
                查看原文
            </MudButton>
        </DialogActions>
    </MudDialog>

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

<style>
    .news-card {
        transition: transform 0.2s ease-in-out;
    }
    
    .news-card:hover {
        transform: translateY(-4px);
    }
    
    .news-title {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        height: 2.8em;
    }
    
    .news-summary {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        height: 4.2em;
    }
</style>

@code {
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private string _selectedSource = "";
    private int _articleCount = 10;
    private bool _showFilter = false;
    private string _filterSource = "";
    private string _searchKeyword = "";
    private int _displayCount = 12;
    
    private List<string> _newsSources = new();
    private List<NewsArticle> _newsArticles = new();
    private List<NewsArticle> _filteredArticles = new();
    
    private bool _showArticleDialog = false;
    private NewsArticle? _selectedArticle;
    private DialogOptions _dialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadNewsSources();
        await LoadNewsHistory();
    }

    private async Task LoadNewsSources()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/automation/news/sources");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.sources != null)
                {
                    _newsSources = System.Text.Json.JsonSerializer.Deserialize<List<string>>(
                        result.sources.ToString()) ?? new List<string>();
                    
                    if (_newsSources.Count > 0)
                    {
                        _selectedSource = _newsSources[0];
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"加载新闻源失败: {ex.Message}");
        }
    }

    private async Task FetchNews()
    {
        if (string.IsNullOrEmpty(_selectedSource)) return;

        _isLoading = true;
        _loadingMessage = $"正在获取 {_selectedSource} 的新闻...";
        
        try
        {
            var response = await HttpClient.GetAsync($"/api/automation/news/{_selectedSource}?maxArticles={_articleCount}");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.articles != null)
                {
                    var newArticles = System.Text.Json.JsonSerializer.Deserialize<List<NewsArticle>>(
                        result.articles.ToString()) ?? new List<NewsArticle>();
                    
                    await NotificationService.ShowSuccessAsync($"成功获取 {newArticles.Count} 篇新闻");
                    await LoadNewsHistory();
                }
            }
            else
            {
                await NotificationService.ShowErrorAsync("获取新闻失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"获取新闻时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task FetchAllSources()
    {
        _isLoading = true;
        _loadingMessage = "正在批量获取所有新闻源...";
        
        try
        {
            var request = new
            {
                Sources = _newsSources,
                ArticlesPerSource = 5
            };

            var response = await HttpClient.PostAsJsonAsync("/api/automation/news/batch", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.articles != null)
                {
                    var newArticles = System.Text.Json.JsonSerializer.Deserialize<List<NewsArticle>>(
                        result.articles.ToString()) ?? new List<NewsArticle>();
                    
                    await NotificationService.ShowSuccessAsync($"成功获取 {newArticles.Count} 篇新闻");
                    await LoadNewsHistory();
                }
            }
            else
            {
                await NotificationService.ShowErrorAsync("批量获取新闻失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"批量获取新闻时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadNewsHistory()
    {
        try
        {
            var response = await HttpClient.GetAsync("/api/automation/news/articles?count=100");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.articles != null)
                {
                    _newsArticles = System.Text.Json.JsonSerializer.Deserialize<List<NewsArticle>>(
                        result.articles.ToString()) ?? new List<NewsArticle>();
                    
                    ApplyFilter();
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"加载新闻历史失败: {ex.Message}");
        }
    }

    private void ToggleFilter()
    {
        _showFilter = !_showFilter;
    }

    private void ApplyFilter()
    {
        _filteredArticles = _newsArticles.Where(article =>
        {
            var matchesSource = string.IsNullOrEmpty(_filterSource) || article.Source.Equals(_filterSource, StringComparison.OrdinalIgnoreCase);
            var matchesKeyword = string.IsNullOrEmpty(_searchKeyword) || 
                                article.Title.Contains(_searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                                article.Content.Contains(_searchKeyword, StringComparison.OrdinalIgnoreCase);
            
            return matchesSource && matchesKeyword;
        }).OrderByDescending(a => a.PublishedAt).ToList();
        
        _displayCount = 12; // 重置显示数量
    }

    private void LoadMore()
    {
        _displayCount += 12;
    }

    private void ViewArticle(NewsArticle article)
    {
        _selectedArticle = article;
        _showArticleDialog = true;
    }

    private void CloseArticleDialog()
    {
        _showArticleDialog = false;
        _selectedArticle = null;
    }
}
