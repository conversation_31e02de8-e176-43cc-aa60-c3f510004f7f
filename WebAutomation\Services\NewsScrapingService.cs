using System.Text.RegularExpressions;
using HtmlAgilityPack;
using WebAutomation.Models;

namespace WebAutomation.Services
{
    /// <summary>
    /// 新闻爬虫服务
    /// </summary>
    public class NewsScrapingService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<NewsScrapingService> _logger;

        // 支持的新闻源配置
        private readonly Dictionary<string, NewsSourceConfig> _newsSources = new()
        {
            ["36kr"] = new NewsSourceConfig
            {
                Name = "36氪",
                BaseUrl = "https://36kr.com",
                ListUrl = "https://36kr.com/newsflashes",
                ListSelector = ".newsflash-item",
                TitleSelector = ".newsflash-item-title",
                LinkSelector = "a",
                TimeSelector = ".newsflash-item-time",
                ContentSelector = ".newsflash-item-desc"
            },
            ["ithome"] = new NewsSourceConfig
            {
                Name = "IT之家",
                BaseUrl = "https://www.ithome.com",
                ListUrl = "https://www.ithome.com/",
                ListSelector = ".lst li",
                TitleSelector = "a",
                LinkSelector = "a",
                TimeSelector = ".time",
                ContentSelector = ".content"
            },
            ["cnbeta"] = new NewsSourceConfig
            {
                Name = "cnBeta",
                BaseUrl = "https://www.cnbeta.com",
                ListUrl = "https://www.cnbeta.com/",
                ListSelector = ".item",
                TitleSelector = ".title a",
                LinkSelector = ".title a",
                TimeSelector = ".time",
                ContentSelector = ".summary"
            },
            ["techcrunch"] = new NewsSourceConfig
            {
                Name = "TechCrunch",
                BaseUrl = "https://techcrunch.com",
                ListUrl = "https://techcrunch.com/",
                ListSelector = ".post-block",
                TitleSelector = ".post-block__title__link",
                LinkSelector = ".post-block__title__link",
                TimeSelector = ".river-byline__time",
                ContentSelector = ".post-block__content"
            }
        };

        public NewsScrapingService(HttpClient httpClient, ILogger<NewsScrapingService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            
            // 设置User-Agent
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        }

        /// <summary>
        /// 获取支持的新闻源列表
        /// </summary>
        public List<string> GetSupportedNewsSources()
        {
            return _newsSources.Keys.ToList();
        }

        /// <summary>
        /// 爬取指定新闻源的文章列表
        /// </summary>
        public async Task<List<NewsArticle>> ScrapeNewsListAsync(string source, int maxArticles = 10)
        {
            var articles = new List<NewsArticle>();

            if (!_newsSources.TryGetValue(source.ToLower(), out var config))
            {
                _logger.LogError("不支持的新闻源: {Source}", source);
                return articles;
            }

            try
            {
                _logger.LogInformation("开始爬取 {Source} 新闻列表", config.Name);

                var response = await _httpClient.GetAsync(config.ListUrl);
                response.EnsureSuccessStatusCode();
                
                var html = await response.Content.ReadAsStringAsync();
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var articleNodes = doc.DocumentNode.SelectNodes(config.ListSelector);
                if (articleNodes == null)
                {
                    _logger.LogWarning("未找到文章列表节点: {Source}", source);
                    return articles;
                }

                var count = 0;
                foreach (var node in articleNodes)
                {
                    if (count >= maxArticles) break;

                    try
                    {
                        var article = ExtractArticleFromNode(node, config);
                        if (article != null && !string.IsNullOrEmpty(article.Title))
                        {
                            articles.Add(article);
                            count++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "提取文章信息失败");
                    }
                }

                _logger.LogInformation("成功爬取 {Source} {Count} 篇文章", config.Name, articles.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取 {Source} 新闻列表失败", source);
            }

            return articles;
        }

        /// <summary>
        /// 从节点提取文章信息
        /// </summary>
        private NewsArticle? ExtractArticleFromNode(HtmlNode node, NewsSourceConfig config)
        {
            try
            {
                var titleNode = node.SelectSingleNode(config.TitleSelector);
                var linkNode = node.SelectSingleNode(config.LinkSelector);
                var timeNode = node.SelectSingleNode(config.TimeSelector);
                var contentNode = node.SelectSingleNode(config.ContentSelector);

                if (titleNode == null || linkNode == null)
                    return null;

                var title = CleanText(titleNode.InnerText);
                var relativeUrl = linkNode.GetAttributeValue("href", "");
                
                if (string.IsNullOrEmpty(title) || string.IsNullOrEmpty(relativeUrl))
                    return null;

                // 构建完整URL
                var url = relativeUrl.StartsWith("http") ? relativeUrl : 
                         new Uri(new Uri(config.BaseUrl), relativeUrl).ToString();

                var article = new NewsArticle
                {
                    Title = title,
                    Url = url,
                    Source = config.Name,
                    PublishedAt = ParsePublishTime(timeNode?.InnerText),
                    Summary = contentNode != null ? CleanText(contentNode.InnerText) : "",
                    Tags = ExtractTags(title)
                };

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取文章节点信息失败");
                return null;
            }
        }

        /// <summary>
        /// 爬取单篇新闻文章的详细内容
        /// </summary>
        public async Task<NewsArticle?> ScrapeNewsArticleAsync(string url, string source)
        {
            try
            {
                _logger.LogInformation("爬取文章详情: {Url}", url);

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                
                var html = await response.Content.ReadAsStringAsync();
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                var article = new NewsArticle
                {
                    Url = url,
                    Source = source,
                    Title = ExtractTitle(doc),
                    Content = ExtractContent(doc),
                    PublishedAt = ExtractPublishTime(doc),
                    Author = ExtractAuthor(doc),
                    Tags = ExtractTagsFromContent(doc)
                };

                // 生成摘要
                if (!string.IsNullOrEmpty(article.Content))
                {
                    article.Summary = GenerateSummary(article.Content);
                }

                _logger.LogInformation("文章爬取成功: {Title}", article.Title);
                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "爬取文章详情失败: {Url}", url);
                return null;
            }
        }

        /// <summary>
        /// 批量爬取多个新闻源
        /// </summary>
        public async Task<List<NewsArticle>> ScrapeMultipleSourcesAsync(List<string> sources, int articlesPerSource = 5)
        {
            var allArticles = new List<NewsArticle>();
            var tasks = new List<Task<List<NewsArticle>>>();

            foreach (var source in sources)
            {
                if (_newsSources.ContainsKey(source.ToLower()))
                {
                    tasks.Add(ScrapeNewsListAsync(source, articlesPerSource));
                }
            }

            try
            {
                var results = await Task.WhenAll(tasks);
                foreach (var articles in results)
                {
                    allArticles.AddRange(articles);
                }

                // 按发布时间排序
                allArticles = allArticles.OrderByDescending(a => a.PublishedAt).ToList();

                _logger.LogInformation("批量爬取完成，共获取 {Count} 篇文章", allArticles.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量爬取失败");
            }

            return allArticles;
        }

        /// <summary>
        /// 提取文章标题
        /// </summary>
        private string ExtractTitle(HtmlDocument doc)
        {
            var selectors = new[] { "h1", ".title", ".article-title", ".post-title", "title" };
            
            foreach (var selector in selectors)
            {
                var node = doc.DocumentNode.SelectSingleNode($"//{selector}");
                if (node != null)
                {
                    var title = CleanText(node.InnerText);
                    if (!string.IsNullOrEmpty(title) && title.Length > 5)
                    {
                        return title;
                    }
                }
            }

            return "";
        }

        /// <summary>
        /// 提取文章内容
        /// </summary>
        private string ExtractContent(HtmlDocument doc)
        {
            var selectors = new[] 
            { 
                ".article-content", 
                ".post-content", 
                ".content", 
                "article", 
                ".entry-content",
                ".article-body"
            };

            foreach (var selector in selectors)
            {
                var node = doc.DocumentNode.SelectSingleNode($"//{selector}");
                if (node != null)
                {
                    // 移除脚本和样式
                    var scriptsAndStyles = node.SelectNodes(".//script | .//style");
                    if (scriptsAndStyles != null)
                    {
                        foreach (var element in scriptsAndStyles)
                        {
                            element.Remove();
                        }
                    }

                    var content = CleanText(node.InnerText);
                    if (!string.IsNullOrEmpty(content) && content.Length > 100)
                    {
                        return content;
                    }
                }
            }

            return "";
        }

        /// <summary>
        /// 提取发布时间
        /// </summary>
        private DateTime ExtractPublishTime(HtmlDocument doc)
        {
            var selectors = new[] { ".time", ".date", ".publish-time", ".created-at", "time" };
            
            foreach (var selector in selectors)
            {
                var node = doc.DocumentNode.SelectSingleNode($"//{selector}");
                if (node != null)
                {
                    var timeText = node.InnerText ?? node.GetAttributeValue("datetime", "");
                    var publishTime = ParsePublishTime(timeText);
                    if (publishTime != DateTime.MinValue)
                    {
                        return publishTime;
                    }
                }
            }

            return DateTime.UtcNow;
        }

        /// <summary>
        /// 提取作者
        /// </summary>
        private string ExtractAuthor(HtmlDocument doc)
        {
            var selectors = new[] { ".author", ".by-author", ".writer", ".byline" };
            
            foreach (var selector in selectors)
            {
                var node = doc.DocumentNode.SelectSingleNode($"//{selector}");
                if (node != null)
                {
                    var author = CleanText(node.InnerText);
                    if (!string.IsNullOrEmpty(author))
                    {
                        return author;
                    }
                }
            }

            return "";
        }

        /// <summary>
        /// 从内容中提取标签
        /// </summary>
        private List<string> ExtractTagsFromContent(HtmlDocument doc)
        {
            var tags = new List<string>();

            // 查找标签元素
            var tagNodes = doc.DocumentNode.SelectNodes(".//span[contains(@class, 'tag')] | .//a[contains(@class, 'tag')]");
            if (tagNodes != null)
            {
                foreach (var node in tagNodes)
                {
                    var tag = CleanText(node.InnerText);
                    if (!string.IsNullOrEmpty(tag) && !tags.Contains(tag))
                    {
                        tags.Add(tag);
                    }
                }
            }

            return tags;
        }

        /// <summary>
        /// 解析发布时间
        /// </summary>
        private DateTime ParsePublishTime(string? timeText)
        {
            if (string.IsNullOrEmpty(timeText))
                return DateTime.MinValue;

            // 尝试多种时间格式
            var formats = new[]
            {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd",
                "MM-dd HH:mm",
                "HH:mm"
            };

            foreach (var format in formats)
            {
                if (DateTime.TryParseExact(timeText, format, null, System.Globalization.DateTimeStyles.None, out var result))
                {
                    return result;
                }
            }

            // 尝试自然语言解析
            if (DateTime.TryParse(timeText, out var parsed))
            {
                return parsed;
            }

            return DateTime.MinValue;
        }

        /// <summary>
        /// 从标题提取标签
        /// </summary>
        private List<string> ExtractTags(string title)
        {
            var tags = new List<string>();

            // 技术相关关键词
            var techKeywords = new[]
            {
                "AI", "人工智能", "机器学习", "深度学习", "区块链", "比特币", "加密货币",
                "云计算", "大数据", "物联网", "5G", "VR", "AR", "元宇宙",
                "苹果", "谷歌", "微软", "亚马逊", "特斯拉", "华为", "小米",
                "iPhone", "Android", "Windows", "Linux", "开源"
            };

            foreach (var keyword in techKeywords)
            {
                if (title.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    tags.Add(keyword);
                }
            }

            return tags;
        }

        /// <summary>
        /// 生成文章摘要
        /// </summary>
        private string GenerateSummary(string content, int maxLength = 200)
        {
            if (string.IsNullOrEmpty(content))
                return "";

            // 简单的摘要生成：取前几句话
            var sentences = content.Split(new[] { '。', '！', '？', '.', '!', '?' }, StringSplitOptions.RemoveEmptyEntries);
            var summary = "";
            
            foreach (var sentence in sentences)
            {
                if (summary.Length + sentence.Length > maxLength)
                    break;
                summary += sentence.Trim() + "。";
            }

            return summary.Length > maxLength ? summary.Substring(0, maxLength) + "..." : summary;
        }

        /// <summary>
        /// 清理文本
        /// </summary>
        private string CleanText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            // 移除HTML标签
            text = Regex.Replace(text, @"<[^>]+>", "");
            
            // 移除多余的空白字符
            text = Regex.Replace(text, @"\s+", " ");
            
            // 解码HTML实体
            text = System.Net.WebUtility.HtmlDecode(text);
            
            return text.Trim();
        }

        /// <summary>
        /// 新闻源配置
        /// </summary>
        private class NewsSourceConfig
        {
            public string Name { get; set; } = "";
            public string BaseUrl { get; set; } = "";
            public string ListUrl { get; set; } = "";
            public string ListSelector { get; set; } = "";
            public string TitleSelector { get; set; } = "";
            public string LinkSelector { get; set; } = "";
            public string TimeSelector { get; set; } = "";
            public string ContentSelector { get; set; } = "";
        }
    }
}
