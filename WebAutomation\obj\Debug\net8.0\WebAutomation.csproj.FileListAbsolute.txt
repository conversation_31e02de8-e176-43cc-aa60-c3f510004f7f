C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.AssemblyInfo.cs
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.RazorAssemblyInfo.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.RazorAssemblyInfo.cs
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\selenium-manager\linux\selenium-manager
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\selenium-manager\macos\selenium-manager
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\selenium-manager\windows\selenium-manager.exe
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.staticwebassets.runtime.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.staticwebassets.endpoints.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.exe
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.deps.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.runtimeconfig.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebAutomation.pdb
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\HtmlAgilityPack.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\MudBlazor.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Quartz.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\WebDriver.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\System.Security.Permissions.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\scopedcss\bundle\WebAutomation.styles.css
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutom.D855CE9B.Up2Date
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\refint\WebAutomation.dll
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.pdb
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\WebAutomation.genruntimeconfig.cache
C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\obj\Debug\net8.0\ref\WebAutomation.dll
