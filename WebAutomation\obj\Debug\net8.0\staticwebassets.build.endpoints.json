{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6766"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 05:00:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI="}]}, {"Route": "css/app.grns6og31q.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6766"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 05:00:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "grns6og31q"}, {"Name": "integrity", "Value": "sha256-tLKtj2ER/2DEV5kXVMww+rBUIAkSGm0ZAYo055mwcwI="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "index.h01dewflzc.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:53:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h01dewflzc"}, {"Name": "integrity", "Value": "sha256-YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1249"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:53:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YcTksHT2N3V5RVkQ71bd3vrLBwPz4ww3C2hdfus0XdM="}]}, {"Route": "js/app.6vu70btkbq.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:54:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6vu70btkbq"}, {"Name": "integrity", "Value": "sha256-/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 17:54:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/ouQEQNbFvbVotlhMtjusEDzU5tSpc03tXUmbe9k9y0="}]}]}