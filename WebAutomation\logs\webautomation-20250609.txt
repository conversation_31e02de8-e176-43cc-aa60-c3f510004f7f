2025-06-09 10:56:57.582 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-09 10:56:58.841 +08:00 [INF] 数据库初始化完成
2025-06-09 10:56:58.843 +08:00 [INF] WebAutomation应用程序启动完成
2025-06-09 10:56:58.935 +08:00 [INF] Now listening on: http://localhost:5096
2025-06-09 10:56:58.937 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 10:56:58.940 +08:00 [INF] Hosting environment: Development
2025-06-09 10:56:58.942 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
2025-06-09 10:57:57.483 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 10:57:57.502 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-09 10:57:57.513 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 10:57:57.524 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 10:57:57.539 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 10:57:57.542 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 10:57:57.816 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 10:57:57.878 +08:00 [INF] Executed page /_Host in 345.8461ms
2025-06-09 10:57:57.888 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 10:57:57.907 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 424.8527ms
2025-06-09 10:57:57.920 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/css/app.css - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/js/app.js - null null
2025-06-09 10:57:57.941 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 10:57:57.944 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 22.8962ms
2025-06-09 10:57:57.947 +08:00 [INF] Sending file. Request path: '/css/app.css'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\css\app.css'
2025-06-09 10:57:57.963 +08:00 [INF] Sending file. Request path: '/_framework/blazor.server.js'. Physical path: 'N/A'
2025-06-09 10:57:57.964 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 10:57:57.966 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/css/app.css - 200 6535 text/css 46.1679ms
2025-06-09 10:57:57.970 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 10:57:57.977 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.css'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.css'
2025-06-09 10:57:57.983 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.js'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.js'
2025-06-09 10:57:57.984 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 200 151672 text/javascript 63.3612ms
2025-06-09 10:57:57.987 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - 200 508754 text/css 65.967ms
2025-06-09 10:57:57.987 +08:00 [INF] Sending file. Request path: '/js/app.js'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\js\app.js'
2025-06-09 10:57:57.991 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - 200 44027 text/javascript 70.4902ms
2025-06-09 10:57:58.009 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/js/app.js - 200 9352 text/javascript 87.0392ms
2025-06-09 10:57:58.350 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 10:57:58.357 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 10:57:58.361 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 10:57:58.364 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 13.8248ms
2025-06-09 10:57:58.625 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 10:57:58.634 +08:00 [INF] CORS policy execution successful.
2025-06-09 10:57:58.639 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 10:57:58.647 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 10:57:58.649 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 24.2633ms
2025-06-09 10:57:58.703 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=XOr4T9ka3-lPTtqD474mug - null null
2025-06-09 10:57:58.710 +08:00 [INF] CORS policy execution successful.
2025-06-09 10:57:58.712 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 10:57:58.801 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 10:57:59.173 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/favicon.png - null null
2025-06-09 10:57:59.179 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/favicon.png - 404 0 null 5.5975ms
2025-06-09 10:57:59.184 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/favicon.png, Response status code: 404
2025-06-09 11:42:43.823 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 11:42:43.829 +08:00 [ERR] 错误: 初始化失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:44.561 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 11:42:44.565 +08:00 [ERR] 错误: 初始化失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:47.754 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/news - null null
2025-06-09 11:42:47.758 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 11:42:47.761 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 11:42:47.763 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 11:42:47.765 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 11:42:47.781 +08:00 [INF] Executed page /_Host in 18.7118ms
2025-06-09 11:42:47.784 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 11:42:47.785 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundarynBXwjByS92dOw65T 359
2025-06-09 11:42:47.788 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/news - 200 null text/html; charset=utf-8 34.4128ms
2025-06-09 11:42:47.789 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:47.790 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:47.795 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=XOr4T9ka3-lPTtqD474mug - 101 null null 2689091.9869ms
2025-06-09 11:42:47.795 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/css/app.css - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 11:42:47.805 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.css'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.css'
2025-06-09 11:42:47.805 +08:00 [INF] Sending file. Request path: '/css/app.css'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\css\app.css'
2025-06-09 11:42:47.808 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.js'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.js'
2025-06-09 11:42:47.809 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:47.810 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 11.9469ms
2025-06-09 11:42:47.814 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 11:42:47.815 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - 200 508754 text/css 17.6954ms
2025-06-09 11:42:47.817 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/css/app.css - 200 6535 text/css 18.4435ms
2025-06-09 11:42:47.818 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - 200 44027 text/javascript 19.8948ms
2025-06-09 11:42:47.820 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 34.5759ms
2025-06-09 11:42:47.823 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 11:42:47.825 +08:00 [INF] Sending file. Request path: '/_framework/blazor.server.js'. Physical path: 'N/A'
2025-06-09 11:42:47.828 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/js/app.js - null null
2025-06-09 11:42:47.841 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 11:42:47.844 +08:00 [INF] Sending file. Request path: '/js/app.js'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\js\app.js'
2025-06-09 11:42:47.845 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 200 151672 text/javascript 46.6251ms
2025-06-09 11:42:47.846 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/js/app.js - 200 9352 text/javascript 18.7319ms
2025-06-09 11:42:48.129 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 11:42:48.133 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 11:42:48.134 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 11:42:48.136 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 7.043ms
2025-06-09 11:42:48.150 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 11:42:48.152 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:48.153 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 11:42:48.154 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 11:42:48.155 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 5.2378ms
2025-06-09 11:42:48.157 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=prU6GM1WT3iYw3W35oVarw - null null
2025-06-09 11:42:48.162 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:48.163 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 11:42:50.117 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:52.465 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ai - null null
2025-06-09 11:42:52.475 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 11:42:52.477 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 11:42:52.479 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 11:42:52.480 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 11:42:52.486 +08:00 [INF] Executed page /_Host in 7.4922ms
2025-06-09 11:42:52.488 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 11:42:52.489 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ai - 200 null text/html; charset=utf-8 24.3426ms
2025-06-09 11:42:52.490 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:52.494 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryAFsv8q6jDUg3uJdb 359
2025-06-09 11:42:52.495 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=prU6GM1WT3iYw3W35oVarw - 101 null null 4338.4078ms
2025-06-09 11:42:52.498 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.502 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:52.504 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 11:42:52.504 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 11:42:52.506 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:52.510 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 11:42:52.513 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 9.5462ms
2025-06-09 11:42:52.514 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 19.8912ms
2025-06-09 11:42:52.516 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 11:42:52.519 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 11:42:52.524 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 11:42:52.528 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 24.8008ms
2025-06-09 11:42:52.539 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 11:42:52.548 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 11:42:52.550 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 11:42:52.551 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 12.5832ms
2025-06-09 11:42:52.564 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 11:42:52.569 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.572 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 11:42:52.574 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 11:42:52.576 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 11.6129ms
2025-06-09 11:42:52.587 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=AELCmwj_OnR8vM6QgEPiyQ - null null
2025-06-09 11:42:52.592 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.594 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 11:42:54.566 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:54.569 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryd7rcdOQCBrDGecPM 359
2025-06-09 11:42:54.570 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=AELCmwj_OnR8vM6QgEPiyQ - 101 null null 1983.4417ms
2025-06-09 11:42:54.576 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:54.580 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:54.582 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:54.583 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 14.195ms
2025-06-09 12:25:08.610 +08:00 [INF] Application is shutting down...
