2025-06-09 10:56:57.582 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-09 10:56:58.841 +08:00 [INF] 数据库初始化完成
2025-06-09 10:56:58.843 +08:00 [INF] WebAutomation应用程序启动完成
2025-06-09 10:56:58.935 +08:00 [INF] Now listening on: http://localhost:5096
2025-06-09 10:56:58.937 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 10:56:58.940 +08:00 [INF] Hosting environment: Development
2025-06-09 10:56:58.942 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
2025-06-09 10:57:57.483 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 10:57:57.502 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-09 10:57:57.513 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 10:57:57.524 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 10:57:57.539 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 10:57:57.542 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 10:57:57.816 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 10:57:57.878 +08:00 [INF] Executed page /_Host in 345.8461ms
2025-06-09 10:57:57.888 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 10:57:57.907 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 424.8527ms
2025-06-09 10:57:57.920 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/css/app.css - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - null null
2025-06-09 10:57:57.921 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/js/app.js - null null
2025-06-09 10:57:57.941 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 10:57:57.944 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 22.8962ms
2025-06-09 10:57:57.947 +08:00 [INF] Sending file. Request path: '/css/app.css'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\css\app.css'
2025-06-09 10:57:57.963 +08:00 [INF] Sending file. Request path: '/_framework/blazor.server.js'. Physical path: 'N/A'
2025-06-09 10:57:57.964 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 10:57:57.966 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/css/app.css - 200 6535 text/css 46.1679ms
2025-06-09 10:57:57.970 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 10:57:57.977 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.css'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.css'
2025-06-09 10:57:57.983 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.js'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.js'
2025-06-09 10:57:57.984 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 200 151672 text/javascript 63.3612ms
2025-06-09 10:57:57.987 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - 200 508754 text/css 65.967ms
2025-06-09 10:57:57.987 +08:00 [INF] Sending file. Request path: '/js/app.js'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\js\app.js'
2025-06-09 10:57:57.991 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - 200 44027 text/javascript 70.4902ms
2025-06-09 10:57:58.009 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/js/app.js - 200 9352 text/javascript 87.0392ms
2025-06-09 10:57:58.350 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 10:57:58.357 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 10:57:58.361 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 10:57:58.364 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 13.8248ms
2025-06-09 10:57:58.625 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 10:57:58.634 +08:00 [INF] CORS policy execution successful.
2025-06-09 10:57:58.639 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 10:57:58.647 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 10:57:58.649 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 24.2633ms
2025-06-09 10:57:58.703 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=XOr4T9ka3-lPTtqD474mug - null null
2025-06-09 10:57:58.710 +08:00 [INF] CORS policy execution successful.
2025-06-09 10:57:58.712 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 10:57:58.801 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 10:57:59.173 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/favicon.png - null null
2025-06-09 10:57:59.179 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/favicon.png - 404 0 null 5.5975ms
2025-06-09 10:57:59.184 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/favicon.png, Response status code: 404
2025-06-09 11:42:43.823 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 11:42:43.829 +08:00 [ERR] 错误: 初始化失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:44.561 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 11:42:44.565 +08:00 [ERR] 错误: 初始化失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:47.754 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/news - null null
2025-06-09 11:42:47.758 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 11:42:47.761 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 11:42:47.763 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 11:42:47.765 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 11:42:47.781 +08:00 [INF] Executed page /_Host in 18.7118ms
2025-06-09 11:42:47.784 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 11:42:47.785 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundarynBXwjByS92dOw65T 359
2025-06-09 11:42:47.788 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/news - 200 null text/html; charset=utf-8 34.4128ms
2025-06-09 11:42:47.789 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:47.790 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:47.795 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=XOr4T9ka3-lPTtqD474mug - 101 null null 2689091.9869ms
2025-06-09 11:42:47.795 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/css/app.css - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 11:42:47.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 11:42:47.805 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.css'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.css'
2025-06-09 11:42:47.805 +08:00 [INF] Sending file. Request path: '/css/app.css'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\css\app.css'
2025-06-09 11:42:47.808 +08:00 [INF] Sending file. Request path: '/_content/MudBlazor/MudBlazor.min.js'. Physical path: 'C:\Users\<USER>\.nuget\packages\mudblazor\6.11.2\staticwebassets\MudBlazor.min.js'
2025-06-09 11:42:47.809 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:47.810 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 11.9469ms
2025-06-09 11:42:47.814 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 11:42:47.815 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.css - 200 508754 text/css 17.6954ms
2025-06-09 11:42:47.817 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/css/app.css - 200 6535 text/css 18.4435ms
2025-06-09 11:42:47.818 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/MudBlazor/MudBlazor.min.js - 200 44027 text/javascript 19.8948ms
2025-06-09 11:42:47.820 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 34.5759ms
2025-06-09 11:42:47.823 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 11:42:47.825 +08:00 [INF] Sending file. Request path: '/_framework/blazor.server.js'. Physical path: 'N/A'
2025-06-09 11:42:47.828 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/js/app.js - null null
2025-06-09 11:42:47.841 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 11:42:47.844 +08:00 [INF] Sending file. Request path: '/js/app.js'. Physical path: 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\wwwroot\js\app.js'
2025-06-09 11:42:47.845 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 200 151672 text/javascript 46.6251ms
2025-06-09 11:42:47.846 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/js/app.js - 200 9352 text/javascript 18.7319ms
2025-06-09 11:42:48.129 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 11:42:48.133 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 11:42:48.134 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 11:42:48.136 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 7.043ms
2025-06-09 11:42:48.150 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 11:42:48.152 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:48.153 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 11:42:48.154 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 11:42:48.155 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 5.2378ms
2025-06-09 11:42:48.157 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=prU6GM1WT3iYw3W35oVarw - null null
2025-06-09 11:42:48.162 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:48.163 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 11:42:50.117 +08:00 [ERR] 错误: 检查系统状态失败: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
2025-06-09 11:42:52.465 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ai - null null
2025-06-09 11:42:52.475 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 11:42:52.477 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 11:42:52.479 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 11:42:52.480 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 11:42:52.486 +08:00 [INF] Executed page /_Host in 7.4922ms
2025-06-09 11:42:52.488 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 11:42:52.489 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ai - 200 null text/html; charset=utf-8 24.3426ms
2025-06-09 11:42:52.490 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:52.494 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryAFsv8q6jDUg3uJdb 359
2025-06-09 11:42:52.495 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=prU6GM1WT3iYw3W35oVarw - 101 null null 4338.4078ms
2025-06-09 11:42:52.498 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.502 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:52.504 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 11:42:52.504 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 11:42:52.506 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:52.510 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 11:42:52.513 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 9.5462ms
2025-06-09 11:42:52.514 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 19.8912ms
2025-06-09 11:42:52.516 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 11:42:52.519 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 11:42:52.524 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 11:42:52.528 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 24.8008ms
2025-06-09 11:42:52.539 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 11:42:52.548 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 11:42:52.550 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 11:42:52.551 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 12.5832ms
2025-06-09 11:42:52.564 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 11:42:52.569 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.572 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 11:42:52.574 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 11:42:52.576 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 11.6129ms
2025-06-09 11:42:52.587 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=AELCmwj_OnR8vM6QgEPiyQ - null null
2025-06-09 11:42:52.592 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:52.594 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 11:42:54.566 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 11:42:54.569 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryd7rcdOQCBrDGecPM 359
2025-06-09 11:42:54.570 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=AELCmwj_OnR8vM6QgEPiyQ - 101 null null 1983.4417ms
2025-06-09 11:42:54.576 +08:00 [INF] CORS policy execution successful.
2025-06-09 11:42:54.580 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 11:42:54.582 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 11:42:54.583 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 14.195ms
2025-06-09 12:25:08.610 +08:00 [INF] Application is shutting down...
2025-06-09 12:27:56.869 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-09 12:27:56.953 +08:00 [INF] 数据库初始化完成
2025-06-09 12:27:56.954 +08:00 [INF] WebAutomation应用程序启动完成
2025-06-09 12:27:56.987 +08:00 [INF] Now listening on: http://localhost:5096
2025-06-09 12:27:56.988 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 12:27:56.989 +08:00 [INF] Hosting environment: Development
2025-06-09 12:27:56.990 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
2025-06-09 12:38:39.409 +08:00 [INF] Application is shutting down...
2025-06-09 12:41:20.204 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-09 12:41:20.289 +08:00 [INF] 数据库初始化完成
2025-06-09 12:41:20.290 +08:00 [INF] WebAutomation应用程序启动完成
2025-06-09 12:41:20.327 +08:00 [INF] Now listening on: http://localhost:5096
2025-06-09 12:41:20.329 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 12:41:20.329 +08:00 [INF] Hosting environment: Development
2025-06-09 12:41:20.332 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
2025-06-09 12:45:26.424 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 12:45:26.451 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-09 12:45:26.468 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:45:26.479 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:45:26.488 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:45:26.492 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:45:26.735 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:45:26.740 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:45:26.859 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:45:26.866 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:45:26.875 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:45:26.881 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:45:26.882 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:45:27.159 +08:00 [INF] Received HTTP response headers after 272.8561ms - 401
2025-06-09 12:45:27.163 +08:00 [INF] End processing HTTP request after 282.3641ms - 401
2025-06-09 12:45:27.171 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:45:27.179 +08:00 [INF] Received HTTP response headers after 437.2772ms - 200
2025-06-09 12:45:27.180 +08:00 [INF] End processing HTTP request after 446.2447ms - 200
2025-06-09 12:45:27.180 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 300.1078ms
2025-06-09 12:45:27.185 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:45:27.188 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 328.6273ms
2025-06-09 12:45:27.216 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:45:27.257 +08:00 [INF] Executed page /_Host in 775.6066ms
2025-06-09 12:45:27.261 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:45:27.268 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:45:27.269 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:45:27.269 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/js/app.js - null null
2025-06-09 12:45:27.276 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:45:27.277 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 854.4079ms
2025-06-09 12:45:27.277 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 8.8272ms
2025-06-09 12:45:27.282 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:45:27.282 +08:00 [INF] The file /js/app.js was not modified
2025-06-09 12:45:27.286 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/css/app.css - null null
2025-06-09 12:45:27.292 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:45:27.292 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:45:27.293 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/js/app.js - 304 null text/javascript 24.4104ms
2025-06-09 12:45:27.296 +08:00 [INF] The file /css/app.css was not modified
2025-06-09 12:45:27.299 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 30.7098ms
2025-06-09 12:45:27.308 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/css/app.css - 304 null text/css 22.0957ms
2025-06-09 12:45:27.453 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:45:27.460 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:45:27.462 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:45:27.464 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 11.4247ms
2025-06-09 12:45:27.607 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:45:27.615 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:45:27.619 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:45:27.625 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:45:27.627 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 19.9521ms
2025-06-09 12:45:27.670 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=4kXrvz9cJbwa7gOW51tUrg - null null
2025-06-09 12:45:27.674 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:45:27.675 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:45:27.788 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:45:27.791 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:45:27.793 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:45:27.797 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:45:27.800 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:45:27.803 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:45:27.805 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:45:27.850 +08:00 [INF] Received HTTP response headers after 42.7961ms - 401
2025-06-09 12:45:27.853 +08:00 [INF] End processing HTTP request after 50.0327ms - 401
2025-06-09 12:45:27.855 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:45:27.858 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 56.2001ms
2025-06-09 12:45:27.859 +08:00 [INF] Received HTTP response headers after 66.6506ms - 200
2025-06-09 12:45:27.861 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:45:27.862 +08:00 [INF] End processing HTTP request after 74.0332ms - 200
2025-06-09 12:45:27.863 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 70.1043ms
2025-06-09 12:45:27.865 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:45:34.133 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 12:45:34.135 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:45:34.136 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:45:34.138 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - application/json; charset=utf-8 null
2025-06-09 12:45:34.141 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:45:34.146 +08:00 [INF] Route matched with {action = "InitializeChrome", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] InitializeChrome(WebAutomation.Models.ChromeOptions) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:45:34.932 +08:00 [INF] 信息: 正在初始化Chrome浏览器...
2025-06-09 12:45:34.932 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:45:34.933 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:45:34.935 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - application/json; charset=utf-8 null
2025-06-09 12:45:34.937 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:45:34.938 +08:00 [INF] Route matched with {action = "InitializeChrome", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] InitializeChrome(WebAutomation.Models.ChromeOptions) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:45:37.679 +08:00 [INF] Chrome浏览器初始化成功
2025-06-09 12:45:37.679 +08:00 [INF] Chrome浏览器初始化成功
2025-06-09 12:45:37.683 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:45:37.685 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation) in 2744.6925ms
2025-06-09 12:45:37.685 +08:00 [INF] Received HTTP response headers after 2751.0465ms - 200
2025-06-09 12:45:37.685 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:45:37.686 +08:00 [INF] End processing HTTP request after 2753.7398ms - 200
2025-06-09 12:45:37.690 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - 200 null application/json; charset=utf-8 2754.8316ms
2025-06-09 12:45:37.693 +08:00 [ERR] 错误: 初始化失败: 'System.Text.Json.JsonElement' does not contain a definition for 'success'
2025-06-09 12:45:37.694 +08:00 [ERR] 保存数据失败: operation_logs.json
System.IO.IOException: The process cannot access the file 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Data\database\operation_logs.json' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.CreateFile(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.File.OpenHandle(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize)
   at System.IO.File.WriteToFileAsync(String path, FileMode mode, String contents, Encoding encoding, CancellationToken cancellationToken)
   at WebAutomation.Data.DatabaseService.SaveAsync[T](String fileName, T data) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Data\DatabaseService.cs:line 64
2025-06-09 12:45:37.705 +08:00 [ERR] 添加操作日志失败
System.IO.IOException: The process cannot access the file 'C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Data\database\operation_logs.json' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.CreateFile(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.File.OpenHandle(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize)
   at System.IO.File.WriteToFileAsync(String path, FileMode mode, String contents, Encoding encoding, CancellationToken cancellationToken)
   at WebAutomation.Data.DatabaseService.SaveAsync[T](String fileName, T data) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Data\DatabaseService.cs:line 64
   at WebAutomation.Data.DatabaseService.AddOperationLogAsync(OperationLog log) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Data\DatabaseService.cs:line 115
2025-06-09 12:45:37.708 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:45:37.709 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation) in 3562.3139ms
2025-06-09 12:45:37.710 +08:00 [INF] Received HTTP response headers after 3572.9931ms - 200
2025-06-09 12:45:37.711 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:45:37.711 +08:00 [INF] End processing HTTP request after 3576.1894ms - 200
2025-06-09 12:45:37.712 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - 200 null application/json; charset=utf-8 3573.8567ms
2025-06-09 12:45:37.713 +08:00 [ERR] 错误: 初始化失败: 'System.Text.Json.JsonElement' does not contain a definition for 'success'
2025-06-09 12:48:33.572 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryPSxdSBOC71B8Ui96 359
2025-06-09 12:48:33.574 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:48:33.574 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:48:33.582 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:48:33.583 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 10.629ms
2025-06-09 12:48:33.607 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:48:33.609 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=4kXrvz9cJbwa7gOW51tUrg - 101 null null 185938.8023ms
2025-06-09 12:53:37.904 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 12:53:37.908 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:53:37.910 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:53:37.914 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:53:37.916 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:53:37.934 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:53:37.936 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:53:37.939 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:53:37.940 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:53:37.941 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:53:37.942 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:53:37.942 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:53:38.151 +08:00 [INF] Received HTTP response headers after 208.4092ms - 401
2025-06-09 12:53:38.152 +08:00 [INF] End processing HTTP request after 210.4839ms - 401
2025-06-09 12:53:38.153 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:53:38.154 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 212.2335ms
2025-06-09 12:53:38.154 +08:00 [INF] Received HTTP response headers after 217.1357ms - 200
2025-06-09 12:53:38.154 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:53:38.156 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 216.7815ms
2025-06-09 12:53:38.155 +08:00 [INF] End processing HTTP request after 221.3167ms - 200
2025-06-09 12:53:38.158 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:53:38.160 +08:00 [INF] Executed page /_Host in 248.3787ms
2025-06-09 12:53:38.163 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:53:38.164 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 260.872ms
2025-06-09 12:53:38.174 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:53:38.174 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:53:38.178 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:53:38.181 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 7.1193ms
2025-06-09 12:53:38.182 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:53:38.184 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:53:38.185 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:53:38.187 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 12.6987ms
2025-06-09 12:53:38.432 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:53:38.434 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:53:38.435 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:53:38.435 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 3.665ms
2025-06-09 12:53:38.593 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:53:38.599 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:53:38.601 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:53:38.603 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:53:38.606 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 12.823ms
2025-06-09 12:53:38.650 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=g2NiJPoMmvjXv4xdDGBSDQ - null null
2025-06-09 12:53:38.653 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:53:38.654 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:53:38.669 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:53:38.670 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:53:38.671 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:53:38.673 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:53:38.674 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:53:38.675 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:53:38.676 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:53:38.721 +08:00 [INF] Received HTTP response headers after 41.3428ms - 401
2025-06-09 12:53:38.722 +08:00 [INF] End processing HTTP request after 46.3942ms - 401
2025-06-09 12:53:38.723 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:53:38.724 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 48.8687ms
2025-06-09 12:53:38.724 +08:00 [INF] Received HTTP response headers after 53.8897ms - 200
2025-06-09 12:53:38.725 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:53:38.730 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 59.007ms
2025-06-09 12:53:38.727 +08:00 [INF] End processing HTTP request after 57.9862ms - 200
2025-06-09 12:53:38.733 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:53:50.691 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:53:50.695 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/chrome/init
2025-06-09 12:53:50.698 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - application/json; charset=utf-8 null
2025-06-09 12:53:50.701 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:53:50.702 +08:00 [INF] Route matched with {action = "InitializeChrome", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] InitializeChrome(WebAutomation.Models.ChromeOptions) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:53:51.724 +08:00 [INF] Chrome浏览器初始化成功
2025-06-09 12:53:51.725 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:53:51.727 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation) in 1022.9569ms
2025-06-09 12:53:51.727 +08:00 [INF] Received HTTP response headers after 1029.8111ms - 200
2025-06-09 12:53:51.728 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.InitializeChrome (WebAutomation)'
2025-06-09 12:53:51.728 +08:00 [INF] End processing HTTP request after 1037.7252ms - 200
2025-06-09 12:53:51.729 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/chrome/init - 200 null application/json; charset=utf-8 1031.4603ms
2025-06-09 12:53:51.731 +08:00 [ERR] 错误: 'System.Text.Json.JsonElement' does not contain a definition for 'success'
2025-06-09 12:53:59.116 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/chrome/automation - null null
2025-06-09 12:53:59.124 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:53:59.126 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:53:59.128 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:53:59.129 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:53:59.134 +08:00 [INF] Executed page /_Host in 6.0251ms
2025-06-09 12:53:59.136 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:53:59.138 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/chrome/automation - 200 null text/html; charset=utf-8 21.4504ms
2025-06-09 12:53:59.144 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryJJ2b22Au6Pvvu96B 359
2025-06-09 12:53:59.145 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:53:59.148 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:53:59.149 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=g2NiJPoMmvjXv4xdDGBSDQ - 101 null null 20499.0792ms
2025-06-09 12:53:59.150 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:53:59.152 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:53:59.152 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:53:59.158 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:53:59.160 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:53:59.160 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 8.7803ms
2025-06-09 12:53:59.162 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:53:59.163 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 19.3846ms
2025-06-09 12:53:59.166 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:53:59.167 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:53:59.173 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 21.0676ms
2025-06-09 12:53:59.186 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:53:59.190 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:53:59.191 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:53:59.194 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 7.5119ms
2025-06-09 12:53:59.203 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:53:59.206 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:53:59.207 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:53:59.208 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:53:59.211 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 7.8903ms
2025-06-09 12:53:59.228 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=b8ICrVcVqdSj-H4-GleA-A - null null
2025-06-09 12:53:59.235 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:53:59.236 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:54:00.168 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/scraping/results?count=20
2025-06-09 12:54:00.171 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/scraping/results?count=20
2025-06-09 12:54:00.172 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/scraping/results?count=20 - null null
2025-06-09 12:54:00.175 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation)'
2025-06-09 12:54:00.180 +08:00 [INF] Route matched with {action = "GetScrapingResults", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetScrapingResults(Int32) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:54:00.187 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.Collections.Generic.List`1[[WebAutomation.Models.ScrapingResult, WebAutomation, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:54:00.190 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation) in 7.4476ms
2025-06-09 12:54:00.191 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation)'
2025-06-09 12:54:00.192 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.ConfigureProperties()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 12:54:00.202 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/scraping/results?count=20 - 500 null text/plain; charset=utf-8 30.2121ms
2025-06-09 12:54:00.203 +08:00 [INF] Received HTTP response headers after 30.6073ms - 500
2025-06-09 12:54:00.210 +08:00 [INF] End processing HTTP request after 42.3473ms - 500
2025-06-09 12:54:08.300 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:08.302 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:08.304 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 12:54:08.307 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:08.311 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:54:08.319 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 12:54:08.320 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:08.321 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:08.324 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 12:54:08.327 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 2.3973ms
2025-06-09 12:54:08.327 +08:00 [INF] Received HTTP response headers after 5.4522ms - 404
2025-06-09 12:54:08.332 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 12:54:08.333 +08:00 [INF] End processing HTTP request after 13.2295ms - 404
2025-06-09 12:54:08.336 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 12:54:08.341 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 12:54:08.342 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 27.907ms
2025-06-09 12:54:08.344 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:08.347 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 12:54:08.352 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 47.7376ms
2025-06-09 12:54:08.352 +08:00 [INF] Received HTTP response headers after 49.2106ms - 500
2025-06-09 12:54:08.355 +08:00 [INF] End processing HTTP request after 55.1506ms - 500
2025-06-09 12:54:08.356 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-271baa21e959e5d766da04148fb8269b-257c82e1a13608ec-00
Transfer-Encoding: chunked

2025-06-09 12:54:09.176 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:09.179 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:09.182 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 12:54:09.187 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:09.188 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:54:09.190 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 12:54:09.192 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:09.194 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:09.196 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 12:54:09.199 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 3.1173ms
2025-06-09 12:54:09.199 +08:00 [INF] Received HTTP response headers after 3.5898ms - 404
2025-06-09 12:54:09.203 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 12:54:09.204 +08:00 [INF] End processing HTTP request after 12.062ms - 404
2025-06-09 12:54:09.207 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 12:54:09.214 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 12:54:09.215 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 24.9475ms
2025-06-09 12:54:09.216 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:09.217 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 12:54:09.226 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 43.5513ms
2025-06-09 12:54:09.226 +08:00 [INF] Received HTTP response headers after 44.6257ms - 500
2025-06-09 12:54:09.231 +08:00 [INF] End processing HTTP request after 55.0705ms - 500
2025-06-09 12:54:09.232 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-271baa21e959e5d766da04148fb8269b-a35c434532ce7f2d-00
Transfer-Encoding: chunked

2025-06-09 12:54:09.820 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:09.824 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:09.826 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 12:54:09.831 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:09.832 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:54:09.834 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 12:54:09.834 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:09.835 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:09.836 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 12:54:09.838 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 1.7876ms
2025-06-09 12:54:09.838 +08:00 [INF] Received HTTP response headers after 2.1026ms - 404
2025-06-09 12:54:09.842 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 12:54:09.843 +08:00 [INF] End processing HTTP request after 8.897ms - 404
2025-06-09 12:54:09.846 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 12:54:09.850 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 12:54:09.851 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 17.7738ms
2025-06-09 12:54:09.852 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:09.853 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 12:54:09.859 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 32.1141ms
2025-06-09 12:54:09.859 +08:00 [INF] Received HTTP response headers after 32.7224ms - 500
2025-06-09 12:54:09.862 +08:00 [INF] End processing HTTP request after 41.8906ms - 500
2025-06-09 12:54:09.863 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-271baa21e959e5d766da04148fb8269b-fa999c8d271ab164-00
Transfer-Encoding: chunked

2025-06-09 12:54:15.281 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:15.284 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 12:54:15.285 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 12:54:15.289 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:15.290 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:54:15.294 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 12:54:15.297 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:15.300 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 12:54:15.303 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 12:54:15.308 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 4.18ms
2025-06-09 12:54:15.312 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 12:54:15.308 +08:00 [INF] Received HTTP response headers after 4.6686ms - 404
2025-06-09 12:54:15.315 +08:00 [INF] End processing HTTP request after 18.1435ms - 404
2025-06-09 12:54:15.317 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 12:54:15.323 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 12:54:15.325 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 31.235ms
2025-06-09 12:54:15.326 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 12:54:15.329 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 12:54:15.336 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 50.661ms
2025-06-09 12:54:15.336 +08:00 [INF] Received HTTP response headers after 51.2736ms - 500
2025-06-09 12:54:15.340 +08:00 [INF] End processing HTTP request after 59.1591ms - 500
2025-06-09 12:54:15.341 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-271baa21e959e5d766da04148fb8269b-3ceff68dad9985c1-00
Transfer-Encoding: chunked

2025-06-09 12:54:18.230 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:54:18.232 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=b8ICrVcVqdSj-H4-GleA-A - 101 null null 19003.5189ms
2025-06-09 12:54:18.234 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryFoG46WRLdxQHLeBq 359
2025-06-09 12:54:18.242 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:54:18.244 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:54:18.248 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:54:18.250 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 15.3907ms
2025-06-09 12:57:10.515 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 12:57:10.518 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:57:10.519 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:57:10.521 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:57:10.523 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:57:10.529 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:57:10.531 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:57:10.534 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:57:10.536 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:57:10.536 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:57:10.537 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:57:10.538 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:57:10.761 +08:00 [INF] Received HTTP response headers after 223.0157ms - 401
2025-06-09 12:57:10.762 +08:00 [INF] End processing HTTP request after 224.9296ms - 401
2025-06-09 12:57:10.763 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:57:10.765 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 227.7354ms
2025-06-09 12:57:10.765 +08:00 [INF] Received HTTP response headers after 232.6032ms - 200
2025-06-09 12:57:10.765 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:57:10.766 +08:00 [INF] End processing HTTP request after 236.6755ms - 200
2025-06-09 12:57:10.766 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 232.4428ms
2025-06-09 12:57:10.767 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:57:10.770 +08:00 [INF] Executed page /_Host in 249.8068ms
2025-06-09 12:57:10.771 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:57:10.772 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 256.8397ms
2025-06-09 12:57:10.781 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:57:10.781 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:57:10.783 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:57:10.784 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 2.7454ms
2025-06-09 12:57:10.784 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:57:10.786 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:57:10.786 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:57:10.788 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 7.0059ms
2025-06-09 12:57:11.036 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:57:11.038 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:57:11.039 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:57:11.040 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 4.0088ms
2025-06-09 12:57:11.200 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:57:11.203 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:11.204 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:57:11.205 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:57:11.206 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 5.3592ms
2025-06-09 12:57:11.259 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=FeLLF0MW-JaCT34Zqx5uJw - null null
2025-06-09 12:57:11.261 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:11.261 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:57:11.280 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:57:11.281 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 12:57:11.282 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 12:57:11.283 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:57:11.284 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 12:57:11.285 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:57:11.285 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 12:57:11.448 +08:00 [INF] Received HTTP response headers after 162.8894ms - 401
2025-06-09 12:57:11.449 +08:00 [INF] End processing HTTP request after 164.8592ms - 401
2025-06-09 12:57:11.450 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 12:57:11.451 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 167.0377ms
2025-06-09 12:57:11.452 +08:00 [INF] Received HTTP response headers after 170.1484ms - 200
2025-06-09 12:57:11.452 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 12:57:11.453 +08:00 [INF] End processing HTTP request after 172.8937ms - 200
2025-06-09 12:57:11.454 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 172.3016ms
2025-06-09 12:57:11.455 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 12:57:34.510 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/scraping/batch - null null
2025-06-09 12:57:34.514 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:57:34.516 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:57:34.518 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:57:34.521 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:57:34.525 +08:00 [INF] Executed page /_Host in 7.0756ms
2025-06-09 12:57:34.528 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:57:34.529 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundary4vIaCENUBPsLMJm1 359
2025-06-09 12:57:34.529 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:57:34.530 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/scraping/batch - 200 null text/html; charset=utf-8 19.8321ms
2025-06-09 12:57:34.534 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:34.536 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=FeLLF0MW-JaCT34Zqx5uJw - 101 null null 23277.1921ms
2025-06-09 12:57:34.543 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:57:34.544 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:57:34.545 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:57:34.548 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:57:34.550 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:57:34.553 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 8.3738ms
2025-06-09 12:57:34.553 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 24.7424ms
2025-06-09 12:57:34.555 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:57:34.559 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:57:34.563 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:57:34.569 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 24.83ms
2025-06-09 12:57:34.582 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:57:34.588 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:57:34.594 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:57:34.595 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 12.886ms
2025-06-09 12:57:34.606 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:57:34.611 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:34.613 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:57:34.617 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:57:34.619 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 12.9737ms
2025-06-09 12:57:34.642 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=YvfiGGeE1qfuGJCYwz80NQ - null null
2025-06-09 12:57:34.647 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:34.648 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:57:35.970 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/chrome/automation - null null
2025-06-09 12:57:35.976 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:57:35.978 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:57:35.979 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:57:35.980 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:57:35.983 +08:00 [INF] Executed page /_Host in 3.9483ms
2025-06-09 12:57:35.984 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:57:35.986 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/chrome/automation - 200 null text/html; charset=utf-8 15.2807ms
2025-06-09 12:57:35.986 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:57:35.990 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryxdBJPvtXIKrZxxLg 359
2025-06-09 12:57:35.991 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=YvfiGGeE1qfuGJCYwz80NQ - 101 null null 1349.3745ms
2025-06-09 12:57:35.994 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:35.997 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:57:35.998 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:57:35.999 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 9.2382ms
2025-06-09 12:57:36.001 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:57:36.002 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:57:36.004 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 3.5196ms
2025-06-09 12:57:36.007 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:57:36.009 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:57:36.010 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:57:36.013 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:57:36.014 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 12.0831ms
2025-06-09 12:57:36.024 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:57:36.026 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:57:36.028 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:57:36.029 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 4.962ms
2025-06-09 12:57:36.049 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:57:36.053 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:36.053 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:57:36.057 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:57:36.059 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 9.7974ms
2025-06-09 12:57:36.060 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=vJA-W_ltPeC868J4yLcuEQ - null null
2025-06-09 12:57:36.064 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:36.065 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:57:40.290 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/chrome/automation - null null
2025-06-09 12:57:40.296 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:57:40.298 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:57:40.300 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:57:40.302 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:57:40.306 +08:00 [INF] Executed page /_Host in 5.533ms
2025-06-09 12:57:40.310 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundarySOnjgJAg2WMtGNeM 359
2025-06-09 12:57:40.310 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:57:40.309 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:57:40.313 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:40.315 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/chrome/automation - 200 null text/html; charset=utf-8 25.3495ms
2025-06-09 12:57:40.317 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=vJA-W_ltPeC868J4yLcuEQ - 101 null null 4256.1595ms
2025-06-09 12:57:40.319 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:57:40.319 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:57:40.325 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:57:40.331 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 12.5767ms
2025-06-09 12:57:40.335 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:57:40.337 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:57:40.342 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:57:40.344 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 34.2886ms
2025-06-09 12:57:40.346 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:57:40.357 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:57:40.359 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 34.1778ms
2025-06-09 12:57:40.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:57:40.382 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:57:40.385 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:57:40.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 16.4853ms
2025-06-09 12:57:40.397 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:57:40.402 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:40.403 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:57:40.405 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:57:40.406 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 9.6623ms
2025-06-09 12:57:40.412 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=gIebhkZfokIv3QM88qVpgg - null null
2025-06-09 12:57:40.415 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:40.416 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 12:57:41.107 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/chrome/automation - null null
2025-06-09 12:57:41.114 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 12:57:41.115 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 12:57:41.117 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 12:57:41.117 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 12:57:41.120 +08:00 [INF] Executed page /_Host in 3.2372ms
2025-06-09 12:57:41.122 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 12:57:41.123 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/chrome/automation - 200 null text/html; charset=utf-8 16.6733ms
2025-06-09 12:57:41.124 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 12:57:41.127 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundarywl6RBclkZ6Gftgu1 359
2025-06-09 12:57:41.127 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=gIebhkZfokIv3QM88qVpgg - 101 null null 715.8648ms
2025-06-09 12:57:41.130 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:41.130 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 12:57:41.130 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 12:57:41.134 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 12:57:41.136 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 12:57:41.139 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 8.6776ms
2025-06-09 12:57:41.141 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 12:57:41.142 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 12:57:41.145 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 12:57:41.146 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 19.2654ms
2025-06-09 12:57:41.147 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 12:57:41.153 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 22.7882ms
2025-06-09 12:57:41.169 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 12:57:41.173 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 12:57:41.175 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 12:57:41.177 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 8.4909ms
2025-06-09 12:57:41.187 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 12:57:41.191 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:41.192 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 12:57:41.193 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 12:57:41.194 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 7.3668ms
2025-06-09 12:57:41.196 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=0EXAAFdRBJ54quet3SghRw - null null
2025-06-09 12:57:41.206 +08:00 [INF] CORS policy execution successful.
2025-06-09 12:57:41.208 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 13:00:40.799 +08:00 [INF] Application is shutting down...
2025-06-09 13:00:40.803 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 13:00:40.805 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=0EXAAFdRBJ54quet3SghRw - 101 null null 179609.3843ms
2025-06-09 13:00:44.920 +08:00 [INF] Chrome浏览器已关闭
2025-06-09 13:01:40.182 +08:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-09 13:01:40.265 +08:00 [INF] 数据库初始化完成
2025-06-09 13:01:40.267 +08:00 [INF] WebAutomation应用程序启动完成
2025-06-09 13:01:40.303 +08:00 [INF] Now listening on: http://localhost:5096
2025-06-09 13:01:40.304 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-09 13:01:40.305 +08:00 [INF] Hosting environment: Development
2025-06-09 13:01:40.306 +08:00 [INF] Content root path: C:\Users\<USER>\Desktop\csharpChrome\WebAutomation
2025-06-09 13:01:49.007 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/chrome - null null
2025-06-09 13:01:49.017 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-09 13:01:49.026 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 13:01:49.032 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 13:01:49.040 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 13:01:49.041 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 13:01:49.231 +08:00 [INF] Executed page /_Host in 194.9547ms
2025-06-09 13:01:49.235 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 13:01:49.237 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundarybIiBC9F1QIxk1cTi 359
2025-06-09 13:01:49.243 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:01:49.246 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 13:01:49.247 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/chrome - 200 null text/html; charset=utf-8 241.0808ms
2025-06-09 13:01:49.250 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 13:01:49.250 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 13:01:49.253 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 13:01:49.255 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 5.0948ms
2025-06-09 13:01:49.256 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 13:01:49.256 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 19.5179ms
2025-06-09 13:01:49.258 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 13:01:49.260 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 13:01:49.269 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 13:01:49.275 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 25.2061ms
2025-06-09 13:01:49.285 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 13:01:49.287 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 13:01:49.289 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 13:01:49.290 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 5.2014ms
2025-06-09 13:01:49.319 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 13:01:49.322 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:01:49.323 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 13:01:49.326 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 13:01:49.326 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 7.359ms
2025-06-09 13:01:49.331 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=G54OWnF690RQRww1BmBrDQ - null null
2025-06-09 13:01:49.332 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:01:49.334 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 13:02:01.565 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/ - null null
2025-06-09 13:02:01.572 +08:00 [INF] Executing endpoint '/_Host'
2025-06-09 13:02:01.575 +08:00 [INF] Route matched with {page = "/_Host", action = "", controller = ""}. Executing page /_Host
2025-06-09 13:02:01.580 +08:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-09 13:02:01.582 +08:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-09 13:02:01.634 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 13:02:01.636 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 13:02:01.750 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 13:02:01.755 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 13:02:01.764 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:01.771 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 13:02:01.772 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 13:02:02.014 +08:00 [INF] Received HTTP response headers after 239.6501ms - 401
2025-06-09 13:02:02.015 +08:00 [INF] End processing HTTP request after 244.5598ms - 401
2025-06-09 13:02:02.019 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 13:02:02.022 +08:00 [INF] Received HTTP response headers after 385.0388ms - 200
2025-06-09 13:02:02.022 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 253.7383ms
2025-06-09 13:02:02.023 +08:00 [INF] End processing HTTP request after 389.423ms - 200
2025-06-09 13:02:02.024 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 13:02:02.026 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 275.9557ms
2025-06-09 13:02:02.037 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 13:02:02.041 +08:00 [INF] Executed page /_Host in 463.3257ms
2025-06-09 13:02:02.042 +08:00 [INF] Executed endpoint '/_Host'
2025-06-09 13:02:02.044 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/ - 200 null text/html; charset=utf-8 479.1312ms
2025-06-09 13:02:02.055 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - null null
2025-06-09 13:02:02.055 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - null null
2025-06-09 13:02:02.061 +08:00 [INF] Executing endpoint 'Blazor static files'
2025-06-09 13:02:02.064 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js - 404 0 null 9.0698ms
2025-06-09 13:02:02.065 +08:00 [INF] The file /_framework/blazor.server.js was not modified
2025-06-09 13:02:02.067 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/_content/Blazored.LocalStorage/blazored-localstorage.js, Response status code: 404
2025-06-09 13:02:02.068 +08:00 [INF] Executed endpoint 'Blazor static files'
2025-06-09 13:02:02.070 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_framework/blazor.server.js - 304 null text/javascript 15.1975ms
2025-06-09 13:02:02.200 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor/initializers - null null
2025-06-09 13:02:02.203 +08:00 [INF] Executing endpoint 'Blazor initializers'
2025-06-09 13:02:02.205 +08:00 [INF] Executed endpoint 'Blazor initializers'
2025-06-09 13:02:02.207 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor/initializers - 200 null application/json; charset=utf-8 6.9083ms
2025-06-09 13:02:02.321 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - null 0
2025-06-09 13:02:02.323 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:02:02.324 +08:00 [INF] Executing endpoint '/_blazor/negotiate'
2025-06-09 13:02:02.324 +08:00 [INF] Executed endpoint '/_blazor/negotiate'
2025-06-09 13:02:02.325 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/negotiate?negotiateVersion=1 - 200 316 application/json 4.0048ms
2025-06-09 13:02:02.358 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/_blazor?id=odGXHmUQ32IyQgT8lRzvcg - null null
2025-06-09 13:02:02.362 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:02:02.364 +08:00 [INF] Executing endpoint '/_blazor'
2025-06-09 13:02:02.395 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 13:02:02.397 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/ai/status
2025-06-09 13:02:02.399 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - null null
2025-06-09 13:02:02.405 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 13:02:02.406 +08:00 [INF] Route matched with {action = "CheckAIStatus", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CheckAIStatus() on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:02.408 +08:00 [INF] Start processing HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 13:02:02.410 +08:00 [INF] Sending HTTP request GET https://api.deepseek.com/v1/models
2025-06-09 13:02:02.453 +08:00 [INF] Received HTTP response headers after 42.3777ms - 401
2025-06-09 13:02:02.456 +08:00 [INF] End processing HTTP request after 48.3392ms - 401
2025-06-09 13:02:02.458 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType10`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 13:02:02.460 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation) in 52.2188ms
2025-06-09 13:02:02.460 +08:00 [INF] Received HTTP response headers after 61.2165ms - 200
2025-06-09 13:02:02.461 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.CheckAIStatus (WebAutomation)'
2025-06-09 13:02:02.462 +08:00 [INF] End processing HTTP request after 67.6283ms - 200
2025-06-09 13:02:02.464 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/ai/status - 200 null application/json; charset=utf-8 64.0789ms
2025-06-09 13:02:02.464 +08:00 [ERR] 错误: 检查系统状态失败: 'System.Text.Json.JsonElement' does not contain a definition for 'isOnline'
2025-06-09 13:02:03.682 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - multipart/form-data; boundary=----WebKitFormBoundaryBa1YUWrXwe4edhRf 359
2025-06-09 13:02:03.689 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 13:02:03.690 +08:00 [INF] CORS policy execution successful.
2025-06-09 13:02:03.693 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=G54OWnF690RQRww1BmBrDQ - 101 null null 14361.7249ms
2025-06-09 13:02:03.695 +08:00 [INF] Executing endpoint 'Blazor disconnect'
2025-06-09 13:02:03.709 +08:00 [INF] Executed endpoint 'Blazor disconnect'
2025-06-09 13:02:03.712 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/_blazor/disconnect - 200 0 null 30.2432ms
2025-06-09 13:02:15.405 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/api/automation/scraping/results?count=20
2025-06-09 13:02:15.411 +08:00 [INF] Sending HTTP request GET http://localhost:5096/api/automation/scraping/results?count=20
2025-06-09 13:02:15.414 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/api/automation/scraping/results?count=20 - null null
2025-06-09 13:02:15.419 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation)'
2025-06-09 13:02:15.425 +08:00 [INF] Route matched with {action = "GetScrapingResults", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetScrapingResults(Int32) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:15.441 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`3[[System.Collections.Generic.List`1[[WebAutomation.Models.ScrapingResult, WebAutomation, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-09 13:02:15.447 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation) in 19.3278ms
2025-06-09 13:02:15.449 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.GetScrapingResults (WebAutomation)'
2025-06-09 13:02:15.452 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.ConfigureProperties()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 13:02:15.470 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/api/automation/scraping/results?count=20 - 500 null text/plain; charset=utf-8 55.6556ms
2025-06-09 13:02:15.470 +08:00 [INF] Received HTTP response headers after 56.2524ms - 500
2025-06-09 13:02:15.472 +08:00 [INF] End processing HTTP request after 67.5737ms - 500
2025-06-09 13:02:24.155 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:24.159 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:24.162 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 13:02:24.169 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:24.175 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:24.191 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 13:02:24.192 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:24.193 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:24.197 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 13:02:24.199 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 2.4583ms
2025-06-09 13:02:24.199 +08:00 [INF] Received HTTP response headers after 4.3959ms - 404
2025-06-09 13:02:24.202 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 13:02:24.203 +08:00 [INF] End processing HTTP request after 11.0988ms - 404
2025-06-09 13:02:24.207 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 13:02:24.216 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 13:02:24.219 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 40.4543ms
2025-06-09 13:02:24.221 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:24.222 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 13:02:24.229 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 67.2032ms
2025-06-09 13:02:24.230 +08:00 [INF] Received HTTP response headers after 70.4683ms - 500
2025-06-09 13:02:24.233 +08:00 [INF] End processing HTTP request after 77.6887ms - 500
2025-06-09 13:02:24.235 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-d54c90c15703fd7de7ebe0b5e5d08b1c-f2b94c824cd4328e-00
Transfer-Encoding: chunked

2025-06-09 13:02:26.238 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:26.241 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:26.243 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 13:02:26.248 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:26.250 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:26.253 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 13:02:26.255 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:26.259 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:26.262 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 13:02:26.264 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 2.3698ms
2025-06-09 13:02:26.264 +08:00 [INF] Received HTTP response headers after 3.1221ms - 404
2025-06-09 13:02:26.272 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 13:02:26.274 +08:00 [INF] End processing HTTP request after 19.2123ms - 404
2025-06-09 13:02:26.278 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 13:02:26.283 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 13:02:26.286 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 33.3937ms
2025-06-09 13:02:26.289 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:26.290 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 13:02:26.299 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 55.5746ms
2025-06-09 13:02:26.299 +08:00 [INF] Received HTTP response headers after 56.7151ms - 500
2025-06-09 13:02:26.305 +08:00 [INF] End processing HTTP request after 67.088ms - 500
2025-06-09 13:02:26.306 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-d54c90c15703fd7de7ebe0b5e5d08b1c-ec449a40b0294760-00
Transfer-Encoding: chunked

2025-06-09 13:02:27.438 +08:00 [INF] Start processing HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:27.443 +08:00 [INF] Sending HTTP request POST http://localhost:5096/api/automation/scraping/webpage
2025-06-09 13:02:27.446 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - application/json; charset=utf-8 null
2025-06-09 13:02:27.450 +08:00 [INF] Executing endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:27.452 +08:00 [INF] Route matched with {action = "ScrapeWebPage", controller = "Automation", page = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScrapeWebPage(WebAutomation.Models.ScrapingRequest) on controller WebAutomation.Controllers.AutomationController (WebAutomation).
2025-06-09 13:02:27.454 +08:00 [INF] 开始爬取网页: www.163.com
2025-06-09 13:02:27.455 +08:00 [INF] Start processing HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:27.457 +08:00 [INF] Sending HTTP request GET http://localhost:5096/www.163.com
2025-06-09 13:02:27.458 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5096/www.163.com - null null
2025-06-09 13:02:27.461 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/www.163.com - 404 0 null 2.6316ms
2025-06-09 13:02:27.461 +08:00 [INF] Received HTTP response headers after 3.1881ms - 404
2025-06-09 13:02:27.466 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5096/www.163.com, Response status code: 404
2025-06-09 13:02:27.468 +08:00 [INF] End processing HTTP request after 12.3048ms - 404
2025-06-09 13:02:27.472 +08:00 [ERR] 爬取网页失败: www.163.com
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at WebAutomation.Services.WebScrapingService.ScrapeWebPageAsync(ScrapingRequest request) in C:\Users\<USER>\Desktop\csharpChrome\WebAutomation\Services\WebScrapingService.cs:line 41
2025-06-09 13:02:27.476 +08:00 [INF] Executing OkObjectResult, writing value of type 'WebAutomation.Models.ScrapingResult'.
2025-06-09 13:02:27.478 +08:00 [INF] Executed action WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation) in 23.638ms
2025-06-09 13:02:27.479 +08:00 [INF] Executed endpoint 'WebAutomation.Controllers.AutomationController.ScrapeWebPage (WebAutomation)'
2025-06-09 13:02:27.480 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-09 13:02:27.488 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5096/api/automation/scraping/webpage - 500 null text/plain; charset=utf-8 42.4341ms
2025-06-09 13:02:27.488 +08:00 [INF] Received HTTP response headers after 43.1599ms - 500
2025-06-09 13:02:27.493 +08:00 [INF] End processing HTTP request after 55.8321ms - 500
2025-06-09 13:02:27.495 +08:00 [ERR] 错误: 爬取失败: System.InvalidOperationException: The JSON property name for 'WebAutomation.Models.ScrapingResult.metaData' collides with another property.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializerPropertyNameConflict(Type type, String propertyName)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.JsonPropertyInfoList.AddPropertyWithConflictResolution(JsonPropertyInfo jsonPropertyInfo, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

HEADERS
=======
Host: localhost:5096
Content-Type: application/json; charset=utf-8
traceparent: 00-d54c90c15703fd7de7ebe0b5e5d08b1c-7c8cf497ba041ddb-00
Transfer-Encoding: chunked

2025-06-09 13:04:48.213 +08:00 [INF] Application is shutting down...
2025-06-09 13:04:48.216 +08:00 [INF] Executed endpoint '/_blazor'
2025-06-09 13:04:48.219 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5096/_blazor?id=odGXHmUQ32IyQgT8lRzvcg - 101 null null 165860.7571ms
2025-06-09 13:04:48.226 +08:00 [INF] Chrome浏览器已关闭
