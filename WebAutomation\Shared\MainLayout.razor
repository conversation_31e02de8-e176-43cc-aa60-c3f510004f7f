@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudSpacer />
        <MudText Typo="Typo.h5" Class="ml-3">🚀 WebAutomation</MudText>
        <MudSpacer />
        <MudIconButton Icon="Icons.Material.Filled.Brightness4" Color="Color.Inherit" OnClick="@ToggleDarkMode" />
    </MudAppBar>
    
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    
    <MudMainContent>
        <div style="padding: 16px; overflow-y: auto; height: calc(100vh - 64px);">
            @Body
        </div>
    </MudMainContent>
</MudLayout>

@* <BlazoredToasts Position="Blazored.Toast.Configuration.ToastPosition.TopRight"
                Timeout="5"
                IconType="Blazored.Toast.Configuration.IconType.FontAwesome"
                SuccessClass="success-toast-override"
                SuccessIcon="fas fa-thumbs-up"
                ErrorIcon="fas fa-bug" /> *@

@code {
    bool _drawerOpen = true;
    bool _isDarkMode = false;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    async Task ToggleDarkMode()
    {
        _isDarkMode = !_isDarkMode;
        await JSRuntime.InvokeVoidAsync("toggleDarkMode", _isDarkMode);
    }
}
