{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"WebAutomation/1.0.0": {"dependencies": {"HtmlAgilityPack": "1.12.1", "Microsoft.AspNetCore.OpenApi": "10.0.0-preview.3.25172.1", "Newtonsoft.Json": "13.0.3", "Selenium.WebDriver": "4.33.0", "Selenium.WebDriver.ChromeDriver": "137.0.7151.6800"}, "runtime": {"WebAutomation.dll": {}}}, "HtmlAgilityPack/1.12.1": {"runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.12.1.0", "fileVersion": "1.12.1.0"}}}, "Microsoft.AspNetCore.OpenApi/10.0.0-preview.3.25172.1": {"dependencies": {"Microsoft.OpenApi": "2.0.0-preview.11"}, "runtime": {"lib/net10.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17201"}}}, "Microsoft.OpenApi/2.0.0-preview.11": {"runtime": {"lib/net8.0/Microsoft.OpenApi.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Selenium.WebDriver/4.33.0": {"runtime": {"lib/net8.0/WebDriver.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Selenium.WebDriver.ChromeDriver/137.0.7151.6800": {}}}, "libraries": {"WebAutomation/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "HtmlAgilityPack/1.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-SP6/2Y26CXtxjXn0Wwsom9Ek35SNWKHEu/IWhNEFejBSSVWWXPRSlpqpBSYWv1SQhYFnwMO01xVbEdK3iRR4hg==", "path": "htmlagilitypack/1.12.1", "hashPath": "htmlagilitypack.1.12.1.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/10.0.0-preview.3.25172.1": {"type": "package", "serviceable": true, "sha512": "sha512-o2A0jvacBI5pyXkGwe5wO09JkBvvUAAbjF/UeAzSvw7uZ1vDBo3QD++vK8BOewf3s8/YNSwsgVerWmSAG2XPeg==", "path": "microsoft.aspnetcore.openapi/10.0.0-preview.3.25172.1", "hashPath": "microsoft.aspnetcore.openapi.10.0.0-preview.3.25172.1.nupkg.sha512"}, "Microsoft.OpenApi/2.0.0-preview.11": {"type": "package", "serviceable": true, "sha512": "sha512-9Iw/y2GEKGeCCMovn2LMdnXW1G0/x7LTeO4lDYFgscw+ytEV+8Xj1Qqj1KH6TUNBqp+lc6oTfq8k7lVPn1MasA==", "path": "microsoft.openapi/2.0.0-preview.11", "hashPath": "microsoft.openapi.2.0.0-preview.11.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Selenium.WebDriver/4.33.0": {"type": "package", "serviceable": true, "sha512": "sha512-5dzwA1cMorwYWudX46zyeUqyZn48TZeCHbSERxbpKezGxZ8hMBpcL12XwG4RGUuQKGH8FiEOJOFK4eCE//uNpQ==", "path": "selenium.webdriver/4.33.0", "hashPath": "selenium.webdriver.4.33.0.nupkg.sha512"}, "Selenium.WebDriver.ChromeDriver/137.0.7151.6800": {"type": "package", "serviceable": true, "sha512": "sha512-iz9aZlE0STsteOtaQWP4OAyh667xyDaJBHhWUGtgHQ7ebJVZz0DA12NoP2H1Y1SE+f3DfsW5Hub66R6IiXeYmQ==", "path": "selenium.webdriver.chromedriver/137.0.7151.6800", "hashPath": "selenium.webdriver.chromedriver.137.0.7151.6800.nupkg.sha512"}}}