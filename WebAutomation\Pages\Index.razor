@page "/"
@inject HttpClient HttpClient
@* @inject IToastService ToastService *@
@inject StateService StateService
@inject NotificationService NotificationService

<PageTitle>WebAutomation - 智能自动化平台</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <!-- 欢迎横幅 -->
    <MudPaper Class="pa-8 mb-6" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h3" Class="mb-2">🚀 WebAutomation</MudText>
                <MudText Typo="Typo.h6" Class="mb-4">智能Chrome自动化控制 & AI驱动的网页爬虫平台</MudText>
                <MudText Typo="Typo.body1" Class="mb-4">
                    集成Chrome浏览器控制、智能网页爬虫、微信网页版管理和AI内容分析于一体的强大自动化平台
                </MudText>
                <MudButton Variant="Variant.Filled" Color="Color.Secondary" Size="Size.Large" StartIcon="Icons.Material.Filled.PlayArrow" OnClick="QuickStart">
                    快速开始
                </MudButton>
            </MudItem>
            <MudItem xs="12" md="4" Class="text-center">
                <MudIcon Icon="Icons.Material.Filled.AutoAwesome" Size="Size.Large" Style="font-size: 8rem; opacity: 0.8;" />
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 系统状态卡片 -->
    <MudGrid Class="mb-6">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4" Style="height: 150px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Computer" Color="@(_chromeStatus ? Color.Success : Color.Error)" Size="Size.Large" Class="mb-2" />
                    <MudText Typo="Typo.h6">Chrome状态</MudText>
                    <MudText Typo="Typo.body2" Color="@(_chromeStatus ? Color.Success : Color.Error)">
                        @(_chromeStatus ? "已连接" : "未连接")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4" Style="height: 150px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Psychology" Color="@(_aiStatus ? Color.Success : Color.Warning)" Size="Size.Large" Class="mb-2" />
                    <MudText Typo="Typo.h6">AI服务</MudText>
                    <MudText Typo="Typo.body2" Color="@(_aiStatus ? Color.Success : Color.Warning)">
                        @(_aiStatus ? "在线" : "离线")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4" Style="height: 150px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Chat" Color="@(_wechatStatus ? Color.Success : Color.Default)" Size="Size.Large" Class="mb-2" />
                    <MudText Typo="Typo.h6">微信状态</MudText>
                    <MudText Typo="Typo.body2" Color="@(_wechatStatus ? Color.Success : Color.Default)">
                        @(_wechatStatus ? "已登录" : "未登录")
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4" Style="height: 150px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Article" Color="Color.Info" Size="Size.Large" Class="mb-2" />
                    <MudText Typo="Typo.h6">新闻源</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Info">
                        @_newsSourceCount 个可用
                    </MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 功能模块卡片 -->
    <MudGrid>
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Web" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">Chrome自动化</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        完全控制Chrome浏览器，实现网页自动化操作、截图、脚本执行等功能
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true" Href="/chrome">
                        进入控制台
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Search" Color="Color.Secondary" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">智能爬虫</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        高效的网页内容提取，支持自定义规则、批量处理和智能内容分析
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Secondary" FullWidth="true" Href="/scraping">
                        开始爬取
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Article" Color="Color.Tertiary" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">新闻监控</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        实时监控科技新闻，支持多个主流新闻源，自动分类和趋势分析
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Tertiary" FullWidth="true" Href="/news">
                        查看新闻
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Chat" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">微信管理</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        微信网页版自动化管理，消息监控、自动回复和智能分析
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Success" FullWidth="true" Href="/wechat">
                        微信控制
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Psychology" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">AI分析</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        基于DeepSeek的智能内容分析，提供摘要生成、情感分析等功能
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true" Href="/ai">
                        AI助手
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 mb-4" Style="height: 200px;">
                <MudCardContent>
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="Icons.Material.Filled.Settings" Color="Color.Dark" Size="Size.Large" Class="mr-3" />
                        <MudText Typo="Typo.h6">系统设置</MudText>
                    </div>
                    <MudText Typo="Typo.body2" Class="mb-3">
                        系统配置管理、API密钥设置、日志查看和性能监控
                    </MudText>
                    <MudButton Variant="Variant.Outlined" Color="Color.Dark" FullWidth="true" Href="/settings">
                        系统管理
                    </MudButton>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private bool _chromeStatus = false;
    private bool _aiStatus = false;
    private bool _wechatStatus = false;
    private int _newsSourceCount = 0;

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        await StateService.InitializeAsync();
        await CheckSystemStatus();
    }

    private async Task CheckSystemStatus()
    {
        try
        {
            // 检查AI状态
            var response = await HttpClient.GetFromJsonAsync<dynamic>("/api/automation/ai/status");
            _aiStatus = response?.isOnline ?? false;

            // 检查新闻源数量
            var newsResponse = await HttpClient.GetFromJsonAsync<dynamic>("/api/automation/news/sources");
            _newsSourceCount = newsResponse?.count ?? 0;

            // 更新系统状态
            await StateService.UpdateSystemStatusAsync(new SystemStatus
            {
                AIOnline = _aiStatus,
                NewsSourceCount = _newsSourceCount,
                ChromeConnected = _chromeStatus,
                WeChatLoggedIn = _wechatStatus
            });

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"检查系统状态失败: {ex.Message}");
        }
    }

    private async Task QuickStart()
    {
        await NotificationService.ShowInfoAsync("正在初始化Chrome浏览器...");

        try
        {
            var options = new ChromeOptions
            {
                Headless = false,
                WindowWidth = 1280,
                WindowHeight = 720
            };

            var response = await HttpClient.PostAsJsonAsync("/api/automation/chrome/init", options);
            var result = await response.Content.ReadFromJsonAsync<dynamic>();

            if (result?.success == true)
            {
                _chromeStatus = true;
                await StateService.UpdateChromeStatusAsync(true);
                await NotificationService.ShowSuccessAsync("Chrome浏览器初始化成功！");
                StateHasChanged();
            }
            else
            {
                await NotificationService.ShowErrorAsync("Chrome浏览器初始化失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"初始化失败: {ex.Message}");
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
