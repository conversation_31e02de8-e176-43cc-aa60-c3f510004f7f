# 测试网页爬虫功能
Write-Host "开始测试网页爬虫功能..." -ForegroundColor Green

# 测试163.com爬取
$url = "https://www.163.com"
Write-Host "正在爬取: $url" -ForegroundColor Yellow

try {
    # 使用HttpClient直接获取网页内容
    $client = New-Object System.Net.Http.HttpClient
    $client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    $response = $client.GetAsync($url).Result
    $content = $response.Content.ReadAsStringAsync().Result
    
    Write-Host "网页获取成功！" -ForegroundColor Green
    Write-Host "内容长度: $($content.Length) 字符" -ForegroundColor Cyan
    
    # 简单的内容分析
    $title = ""
    if ($content -match '<title[^>]*>([^<]+)</title>') {
        $title = $matches[1]
        Write-Host "页面标题: $title" -ForegroundColor Cyan
    }
    
    # 查找科技相关内容
    $techKeywords = @("科技", "数码", "手机", "电脑", "AI", "人工智能", "互联网", "5G", "芯片")
    $foundKeywords = @()
    
    foreach ($keyword in $techKeywords) {
        if ($content -match $keyword) {
            $foundKeywords += $keyword
        }
    }
    
    if ($foundKeywords.Count -gt 0) {
        Write-Host "发现科技相关关键词: $($foundKeywords -join ', ')" -ForegroundColor Green
    }
    
    # 提取链接
    $links = [regex]::Matches($content, 'href="([^"]+)"') | ForEach-Object { $_.Groups[1].Value } | Where-Object { $_ -like "http*" } | Select-Object -First 10
    
    Write-Host "`n=== 发现的链接 (前10个) ===" -ForegroundColor Yellow
    foreach ($link in $links) {
        Write-Host "- $link" -ForegroundColor White
    }
    
    # 查找科技相关链接
    $techLinks = $links | Where-Object { 
        $_ -match "tech|科技|数码|mobile|ai|智能|电脑|手机" 
    }
    
    if ($techLinks.Count -gt 0) {
        Write-Host "`n=== 科技相关链接 ===" -ForegroundColor Green
        foreach ($link in $techLinks) {
            Write-Host "- $link" -ForegroundColor Cyan
        }
    }
    
    # 生成简单摘要
    $summary = $content -replace '<[^>]+>', '' -replace '\s+', ' '
    $summary = $summary.Substring(0, [Math]::Min(500, $summary.Length))
    
    Write-Host "`n=== 内容摘要 (前500字符) ===" -ForegroundColor Yellow
    Write-Host $summary -ForegroundColor White
    
    Write-Host "`n爬取测试完成！" -ForegroundColor Green
    
} catch {
    Write-Host "爬取失败: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($client) {
        $client.Dispose()
    }
}

Write-Host "`n按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
