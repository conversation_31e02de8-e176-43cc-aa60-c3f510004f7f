@page "/wechat"
@inject HttpClient HttpClient
@inject NotificationService NotificationService
@inject StateService StateService

<PageTitle>微信管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">💬 微信管理</MudText>

    <!-- 微信状态卡片 -->
    <MudCard Class="mb-4">
        <MudCardContent>
            <MudGrid AlignItems="Center">
                <MudItem xs="12" md="6">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@(_isLoggedIn ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                 Color="@(_isLoggedIn ? Color.Success : Color.Error)" 
                                 Size="Size.Large" Class="mr-3" />
                        <div>
                            <MudText Typo="Typo.h6">微信状态</MudText>
                            <MudText Typo="Typo.body2" Color="@(_isLoggedIn ? Color.Success : Color.Error)">
                                @(_isLoggedIn ? "已登录" : "未登录")
                            </MudText>
                        </div>
                    </div>
                </MudItem>
                <MudItem xs="12" md="6" Class="text-right">
                    <MudButton Variant="Variant.Filled" 
                               Color="@(_isLoggedIn ? Color.Error : Color.Primary)"
                               StartIcon="@(_isLoggedIn ? Icons.Material.Filled.Logout : Icons.Material.Filled.Login)"
                               OnClick="@(_isLoggedIn ? LogoutWeChat : LoginWeChat)"
                               Disabled="_isLoading">
                        @(_isLoggedIn ? "退出登录" : "登录微信")
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    @if (_isLoggedIn)
    {
        <MudGrid>
            <!-- 聊天列表 -->
            <MudItem xs="12" md="4">
                <MudCard Class="mb-4" Style="height: 600px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">💬 聊天列表</MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                                     Color="Color.Secondary" 
                                     Size="Size.Small"
                                     OnClick="LoadChats">
                                刷新
                            </MudButton>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent Style="height: 500px; overflow-y: auto;">
                        @if (_chatList?.Count > 0)
                        {
                            <MudList>
                                @foreach (var chat in _chatList)
                                {
                                    <MudListItem OnClick="() => SelectChat(chat)" 
                                               Class="@(chat == _selectedChat ? "selected-chat" : "")">
                                        <div class="d-flex align-center">
                                            <MudAvatar Color="Color.Primary" Size="Size.Medium" Class="mr-3">
                                                @chat.Name.Substring(0, 1)
                                            </MudAvatar>
                                            <div class="flex-grow-1">
                                                <MudText Typo="Typo.subtitle2">@chat.Name</MudText>
                                                <MudText Typo="Typo.caption" Class="text-truncate">
                                                    @chat.LastMessage
                                                </MudText>
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    @chat.LastTime
                                                </MudText>
                                            </div>
                                            @if (chat.UnreadCount > 0)
                                            {
                                                <MudBadge Content="@chat.UnreadCount" Color="Color.Error" />
                                            }
                                        </div>
                                    </MudListItem>
                                }
                            </MudList>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Class="text-center">暂无聊天记录</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 消息列表 -->
            <MudItem xs="12" md="8">
                <MudCard Class="mb-4" Style="height: 600px;">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                📝 消息记录 @(_selectedChat != null ? $"- {_selectedChat.Name}" : "")
                            </MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudButton StartIcon="Icons.Material.Filled.Analytics" 
                                     Color="Color.Info" 
                                     Size="Size.Small"
                                     OnClick="AnalyzeMessages"
                                     Disabled="@(_messages?.Count == 0)">
                                AI分析
                            </MudButton>
                            <MudButton StartIcon="Icons.Material.Filled.Refresh" 
                                     Color="Color.Secondary" 
                                     Size="Size.Small"
                                     OnClick="LoadMessages">
                                刷新
                            </MudButton>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent Style="height: 450px; overflow-y: auto;">
                        @if (_messages?.Count > 0)
                        {
                            <div class="message-container">
                                @foreach (var message in _messages.OrderBy(m => m.Timestamp))
                                {
                                    <div class="message-item mb-2">
                                        <div class="d-flex align-start">
                                            <MudAvatar Color="Color.Secondary" Size="Size.Small" Class="mr-2">
                                                @message.Sender.Substring(0, 1)
                                            </MudAvatar>
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-center mb-1">
                                                    <MudText Typo="Typo.caption" Class="mr-2">
                                                        @message.Sender
                                                    </MudText>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                        @message.Timestamp.ToString("HH:mm:ss")
                                                    </MudText>
                                                </div>
                                                <MudText Typo="Typo.body2" Class="message-content">
                                                    @message.Content
                                                </MudText>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Class="text-center">
                                @(_selectedChat != null ? "暂无消息记录" : "请选择一个聊天")
                            </MudText>
                        }
                    </MudCardContent>
                    
                    <!-- 发送消息区域 -->
                    @if (_selectedChat != null)
                    {
                        <MudCardActions>
                            <MudGrid AlignItems="Center">
                                <MudItem xs="10">
                                    <MudTextField @bind-Value="_messageToSend" 
                                                Label="输入消息" 
                                                Placeholder="输入要发送的消息..."
                                                @onkeypress="OnMessageKeyPress" />
                                </MudItem>
                                <MudItem xs="2">
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary" 
                                             FullWidth="true"
                                             StartIcon="Icons.Material.Filled.Send"
                                             OnClick="SendMessage"
                                             Disabled="@(string.IsNullOrEmpty(_messageToSend) || _isLoading)">
                                        发送
                                    </MudButton>
                                </MudItem>
                            </MudGrid>
                        </MudCardActions>
                    }
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- AI分析结果 -->
        @if (!string.IsNullOrEmpty(_analysisResult))
        {
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🤖 AI分析结果</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="code-block">
                        @_analysisResult
                    </div>
                </MudCardContent>
            </MudCard>
        }
    }

    <!-- 加载指示器 -->
    @if (_isLoading)
    {
        <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
            <MudText Typo="Typo.h6" Class="mt-4">@_loadingMessage</MudText>
        </MudOverlay>
    }
</MudContainer>

<style>
    .selected-chat {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.1);
    }
    
    .message-container {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .message-item {
        padding: 8px;
        border-radius: 8px;
        background-color: rgba(var(--mud-palette-surface-rgb), 0.5);
    }
    
    .message-content {
        word-wrap: break-word;
        white-space: pre-wrap;
    }
</style>

@code {
    private bool _isLoading = false;
    private string _loadingMessage = "";
    private bool _isLoggedIn = false;
    private string _messageToSend = "";
    private string _analysisResult = "";
    
    private List<WeChatChat> _chatList = new();
    private List<WeChatMessage> _messages = new();
    private WeChatChat? _selectedChat;

    protected override async Task OnInitializedAsync()
    {
        StateService.OnStateChanged += StateHasChanged;
        _isLoggedIn = StateService.SystemStatus.WeChatLoggedIn;
        
        if (_isLoggedIn)
        {
            await LoadChats();
        }
    }

    private async Task LoginWeChat()
    {
        _isLoading = true;
        _loadingMessage = "正在登录微信网页版...";
        
        try
        {
            var response = await HttpClient.PostAsync("/api/automation/wechat/login", null);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.success == true)
                {
                    _isLoggedIn = true;
                    await StateService.UpdateWeChatStatusAsync(true);
                    await NotificationService.ShowSuccessAsync("微信登录成功");
                    await LoadChats();
                }
                else
                {
                    await NotificationService.ShowErrorAsync("微信登录失败");
                }
            }
            else
            {
                await NotificationService.ShowErrorAsync("微信登录请求失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"微信登录时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LogoutWeChat()
    {
        _isLoggedIn = false;
        await StateService.UpdateWeChatStatusAsync(false);
        _chatList.Clear();
        _messages.Clear();
        _selectedChat = null;
        await NotificationService.ShowInfoAsync("微信已退出登录");
    }

    private async Task LoadChats()
    {
        if (!_isLoggedIn) return;

        try
        {
            var response = await HttpClient.GetAsync("/api/automation/wechat/chats");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.chats != null)
                {
                    _chatList = System.Text.Json.JsonSerializer.Deserialize<List<WeChatChat>>(
                        result.chats.ToString()) ?? new List<WeChatChat>();
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"加载聊天列表失败: {ex.Message}");
        }
    }

    private async Task LoadMessages()
    {
        if (!_isLoggedIn) return;

        try
        {
            var response = await HttpClient.GetAsync("/api/automation/wechat/messages?maxCount=50");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.messages != null)
                {
                    _messages = System.Text.Json.JsonSerializer.Deserialize<List<WeChatMessage>>(
                        result.messages.ToString()) ?? new List<WeChatMessage>();
                }
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"加载消息失败: {ex.Message}");
        }
    }

    private void SelectChat(WeChatChat chat)
    {
        _selectedChat = chat;
        // 这里可以加载特定聊天的消息
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrEmpty(_messageToSend) || _selectedChat == null) return;

        _isLoading = true;
        _loadingMessage = "正在发送消息...";
        
        try
        {
            // 这里应该调用发送消息的API
            await Task.Delay(1000); // 模拟发送
            
            // 添加到本地消息列表
            _messages.Add(new WeChatMessage
            {
                Sender = "我",
                Content = _messageToSend,
                Timestamp = DateTime.Now
            });
            
            _messageToSend = "";
            await NotificationService.ShowSuccessAsync("消息发送成功");
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"发送消息失败: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task AnalyzeMessages()
    {
        if (_messages?.Count == 0) return;

        _isLoading = true;
        _loadingMessage = "正在分析消息...";
        
        try
        {
            var response = await HttpClient.PostAsJsonAsync("/api/automation/wechat/analyze", _messages);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                if (result?.analysis != null)
                {
                    _analysisResult = result.analysis.ToString();
                    await NotificationService.ShowSuccessAsync("消息分析完成");
                }
            }
            else
            {
                await NotificationService.ShowErrorAsync("消息分析失败");
            }
        }
        catch (Exception ex)
        {
            await NotificationService.ShowErrorAsync($"分析消息时发生错误: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task OnMessageKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
